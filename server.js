const express = require('express');
const sqlite3 = require('sqlite3').verbose();
const multer = require('multer');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

// 初始化 Express 应用
const app = express();
const dbFile = 'documents.db';
const db = new sqlite3.Database(dbFile);
const port = 3000;

// 配置数据库WAL模式
db.serialize(() => {
    db.run('PRAGMA journal_mode=WAL');  // 启用WAL模式
    db.run('PRAGMA synchronous=NORMAL'); // 在保证安全的前提下提高性能
    db.run('PRAGMA wal_autocheckpoint=1000'); // 设置自动检查点（约1000页后自动创建检查点）
    db.run('PRAGMA busy_timeout=5000'); // 设置繁忙超时为5秒
});

// 中间件配置
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ limit: '50mb', extended: true }));
app.use(express.static(__dirname));
app.use('/uploads', express.static('uploads'));

// 设置正确的 MIME 类型
app.use((req, res, next) => {
    if (req.url.endsWith('.svg')) {
        res.type('image/svg+xml');
    }
    next();
});

// 文件上传配置
!fs.existsSync('uploads') && fs.mkdirSync('uploads');
const storage = multer.diskStorage({
    destination: 'uploads/',
    filename: (req, file, cb) => {
        // 解码原始文件名
        const decodedName = Buffer.from(file.originalname, 'latin1').toString('utf8');
        // 生成安全的文件名
        const safeName = decodedName.replace(/[^a-zA-Z0-9\u4e00-\u9fa5.-]/g, '_');
        // 添加时间戳避免重名
        const timestamp = Date.now();
        const ext = path.extname(safeName);
        const fileName = `${timestamp}-${path.basename(safeName, ext)}${ext}`;
        cb(null, fileName);
    }
});
const upload = multer({ 
    storage,
    limits: {
        fileSize: 50 * 1024 * 1024, // 设置最大文件大小为50MB
        files: 10 // 限制单次上传文件数量
    },
    fileFilter: (req, file, cb) => {
        // 检查文件大小（通过请求头）
        const fileSize = parseInt(req.headers['content-length']);
        if (fileSize > 50 * 1024 * 1024) {
            cb(new Error('文件大小超过50MB限制'), false);
            return;
        }
        cb(null, true);
    }
});

// 模板文件上传配置
!fs.existsSync('templates') && fs.mkdirSync('templates');
const templateStorage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, 'templates/');
    },
    filename: function (req, file, cb) {
        // 生成时间戳文件名
        const timestamp = Date.now();
        const randomString = Math.random().toString(36).substring(2, 8);
        const ext = path.extname(file.originalname);
        const fileName = `template_${timestamp}_${randomString}${ext}`;
        cb(null, fileName);
    }
});

const templateUpload = multer({ 
    storage: templateStorage,
    limits: {
        fileSize: 50 * 1024 * 1024,
        files: 1 // 模板一次只能上传一个文件
    },
    fileFilter: (req, file, cb) => {
        // 检查文件大小
        const fileSize = parseInt(req.headers['content-length']);
        if (fileSize > 50 * 1024 * 1024) {
            cb(new Error('文件大小超过50MB限制'), false);
            return;
        }
        
        // 检查文件类型（只允许常见的办公文档格式）
        const ext = path.extname(file.originalname).toLowerCase();
        const allowedTypes = ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.pdf', '.txt'];
        if (!allowedTypes.includes(ext)) {
            cb(new Error('只允许上传文档类型的模板文件'), false);
            return;
        }
        
        cb(null, true);
    }
});

// 获取文件类型
const getFileType = (filename) => {
    const ext = path.extname(filename).toLowerCase();
    if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'].includes(ext)) {
        return 'image';
    } else if (ext === '.pdf') {
        return 'pdf';
    } else if (['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'].includes(ext)) {
        return 'office';
    } else {
        return 'other';
    }
};

// 获取文件类型名称
const getFileTypeName = (fileType) => {
    const typeNames = {
        'pdf': 'PDF 文档',
        'office': 'Office 文档',
        'image': '图片',
        'other': '文件'
    };
    return typeNames[fileType] || '文件';
};

// 数据库工具函数
const dbAsync = (method, sql, params = []) => {
    return new Promise((resolve, reject) => {
        db[method](sql, params, (err, result) => {
            err ? reject(err) : resolve(result);
        });
    });
};

// 错误处理中间件
const asyncHandler = (fn) => (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
};

// 添加事务和锁的工具函数
const acquireLock = async (type) => {
    try {
        await dbAsync('run', 'INSERT INTO locks (type, locked_at) VALUES (?, datetime("now"))', [type]);
        return true;
    } catch (error) {
        return false;
    }
};

const releaseLock = async (type) => {
    await dbAsync('run', 'DELETE FROM locks WHERE type = ?', [type]);
};

// 权限验证中间件
const checkPermission = (requiredPermission) => {
    return async (req, res, next) => {
        try {
            const userId = req.headers['user-id'];
            if (!userId) {
                return res.status(401).json({ error: '请先登录' });
            }

            // 获取用户角色和权限
            const userRoles = await dbAsync('all', `
                SELECT r.* 
                FROM roles r
                JOIN user_roles ur ON r.id = ur.role_id
                WHERE ur.user_id = ?
            `, [userId]);

            // 获取角色对应的权限
            const permissions = await dbAsync('all', `
                SELECT DISTINCT p.code
                FROM permissions p
                JOIN role_permissions rp ON p.id = rp.permission_id
                JOIN user_roles ur ON rp.role_id = ur.role_id
                WHERE ur.user_id = ?
            `, [userId]);

            const userPermissions = permissions.map(p => p.code);
            
            // 检查是否有所需权限
            if (!userPermissions.includes(requiredPermission)) {
                return res.status(403).json({ error: '没有操作权限' });
            }

            // 将用户信息和权限添加到请求对象
            req.user = {
                id: userId,
                roles: userRoles,
                permissions: userPermissions
            };

            next();
        } catch (error) {
            next(error);
        }
    };
};

// 检查数据库是否需要初始化
const checkDbExists = async () => {
    try {
        // 检查users表是否存在
        const result = await dbAsync('get', `
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='users'
        `);
        return !!result;
    } catch (err) {
        return false;
    }
};

// 检查并更新数据库结构
const migrateDb = async () => {
    try {
        // 检查documents表是否存在deleted_at字段
        const tableInfo = await dbAsync('all', 'PRAGMA table_info(documents)');
        const hasDeletedAt = tableInfo.some(col => col.name === 'deleted_at');
        const hasDeletedBy = tableInfo.some(col => col.name === 'deleted_by');
        const hasUserId = tableInfo.some(col => col.name === 'user_id');

        if (!hasDeletedAt) {
            console.log('添加 deleted_at 字段...');
            await dbAsync('run', 'ALTER TABLE documents ADD COLUMN deleted_at DATETIME');
        }

        if (!hasDeletedBy) {
            console.log('添加 deleted_by 字段...');
            await dbAsync('run', 'ALTER TABLE documents ADD COLUMN deleted_by INTEGER REFERENCES users(id)');
        }

        if (!hasUserId) {
            console.log('添加 user_id 字段...');
            await dbAsync('run', 'ALTER TABLE documents ADD COLUMN user_id INTEGER REFERENCES users(id)');
        }

        // 检查attachments表是否存在size字段
        const attachmentsInfo = await dbAsync('all', 'PRAGMA table_info(attachments)');
        const hasSize = attachmentsInfo.some(col => col.name === 'size');

        if (!hasSize) {
            console.log('添加 size 字段...');
            await dbAsync('run', 'ALTER TABLE attachments ADD COLUMN size INTEGER');
            
            // 更新现有附件的大小
            const attachments = await dbAsync('all', 'SELECT id, filepath FROM attachments');
            for (const att of attachments) {
                if (fs.existsSync(att.filepath)) {
                    const stats = fs.statSync(att.filepath);
                    await dbAsync('run', 'UPDATE attachments SET size = ? WHERE id = ?', [stats.size, att.id]);
                }
            }
        }

        // 检查templates表是否存在
        const templatesTableExists = await dbAsync('get', `
            SELECT name FROM sqlite_master
            WHERE type='table' AND name='templates'
        `);

        if (!templatesTableExists) {
            console.log('创建 templates 表...');
            await dbAsync('run', `
                CREATE TABLE templates (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    filename TEXT NOT NULL,
                    filepath TEXT NOT NULL,
                    file_type TEXT NOT NULL,
                    size INTEGER,
                    uploaded_by INTEGER NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY(uploaded_by) REFERENCES users(id)
                )
            `);
        }

        // 检查并更新文档编号索引（从按类型分组改为统一编号）
        const oldIndexExists = await dbAsync('get', `
            SELECT name FROM sqlite_master
            WHERE type='index' AND name='idx_doc_type_year_number'
        `);

        if (oldIndexExists) {
            console.log('删除旧的文档编号索引...');
            await dbAsync('run', 'DROP INDEX idx_doc_type_year_number');
        }

        const newIndexExists = await dbAsync('get', `
            SELECT name FROM sqlite_master
            WHERE type='index' AND name='idx_doc_year_number'
        `);

        if (!newIndexExists) {
            console.log('创建新的文档编号索引（统一编号）...');
            await dbAsync('run', `
                CREATE UNIQUE INDEX idx_doc_year_number
                ON documents(year, number)
            `);
        }

        console.log('数据库结构更新完成');
    } catch (err) {
        console.error('数据库迁移失败:', err);
        throw err;
    }
};

// 初始化数据库
const initDb = async () => {
    try {
        // 检查数据库是否已经存在
        const dbExists = await checkDbExists();
        if (dbExists) {
            console.log('数据库已存在，检查是否需要更新结构...');
            await migrateDb();
            return;
        }

        console.log('开始初始化数据库...');
        
        // 创建表
        await dbAsync('run', `
            CREATE TABLE IF NOT EXISTS locks (
                type TEXT PRIMARY KEY,
                locked_at DATETIME NOT NULL
            )
        `);

        await dbAsync('run', `
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `);

        await dbAsync('run', `
            CREATE TABLE IF NOT EXISTS roles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT
            )
        `);

        await dbAsync('run', `
            CREATE TABLE IF NOT EXISTS permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                description TEXT
            )
        `);

        await dbAsync('run', `
            CREATE TABLE IF NOT EXISTS user_roles (
                user_id INTEGER NOT NULL,
                role_id INTEGER NOT NULL,
                PRIMARY KEY (user_id, role_id),
                FOREIGN KEY (user_id) REFERENCES users(id),
                FOREIGN KEY (role_id) REFERENCES roles(id)
            )
        `);

        await dbAsync('run', `
            CREATE TABLE IF NOT EXISTS role_permissions (
                role_id INTEGER NOT NULL,
                permission_id INTEGER NOT NULL,
                PRIMARY KEY (role_id, permission_id),
                FOREIGN KEY (role_id) REFERENCES roles(id),
                FOREIGN KEY (permission_id) REFERENCES permissions(id)
            )
        `);

        await dbAsync('run', `
            CREATE TABLE IF NOT EXISTS documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                type TEXT NOT NULL,
                number INTEGER NOT NULL,
                year INTEGER NOT NULL,
                title TEXT NOT NULL,
                user_id INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY(user_id) REFERENCES users(id)
            )
        `);

        // 添加联合唯一索引确保同年度号码不重复（不区分类型）
        await dbAsync('run', `
            CREATE UNIQUE INDEX IF NOT EXISTS idx_doc_year_number
            ON documents(year, number)
        `);

        await dbAsync('run', `
            CREATE TABLE IF NOT EXISTS attachments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                document_id INTEGER NOT NULL,
                filename TEXT NOT NULL,
                filepath TEXT NOT NULL,
                file_type TEXT NOT NULL,
                size INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY(document_id) REFERENCES documents(id)
            )
        `);

        // 创建模板表
        await dbAsync('run', `
            CREATE TABLE IF NOT EXISTS templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                filename TEXT NOT NULL,
                filepath TEXT NOT NULL,
                file_type TEXT NOT NULL,
                size INTEGER,
                uploaded_by INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY(uploaded_by) REFERENCES users(id)
            )
        `);

        // 检查是否已存在角色
        const rolesExist = await dbAsync('get', 'SELECT 1 FROM roles LIMIT 1');
        if (!rolesExist) {
            // 插入初始角色
            await dbAsync('run', `INSERT INTO roles (name, description) VALUES ('admin', '管理员')`);
            await dbAsync('run', `INSERT INTO roles (name, description) VALUES ('user', '普通用户')`);
        }

        // 检查是否已存在权限
        const permissionsExist = await dbAsync('get', 'SELECT 1 FROM permissions LIMIT 1');
        if (!permissionsExist) {
            // 插入初始权限
            await dbAsync('run', `INSERT INTO permissions (code, name, description) VALUES ('export_excel', '导出Excel', '允许导出文档到Excel')`);
            await dbAsync('run', `INSERT INTO permissions (code, name, description) VALUES ('download_attachments', '下载附件', '允许下载文档附件')`);
        }

        // 检查管理员角色权限是否已分配
        const adminRoleId = 1;
        const rolePermissionsExist = await dbAsync('get', 'SELECT 1 FROM role_permissions LIMIT 1');
        if (!rolePermissionsExist) {
            // 为管理员角色分配所有权限
            await dbAsync('run', `INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)`, [adminRoleId, 1]);
            await dbAsync('run', `INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)`, [adminRoleId, 2]);
        }

        // 检查默认管理员账号是否存在
        const adminExists = await dbAsync('get', 'SELECT 1 FROM users WHERE username = ?', ['admin']);
        if (!adminExists) {
            // 创建默认管理员账号
            const adminResult = await new Promise((resolve, reject) => {
                db.run(
                    'INSERT INTO users (username, password) VALUES (?, ?)',
                    ['admin', 'Bl123456'],  // 管理员默认密码
                    function(err) {
                        if (err) reject(err);
                        else resolve({ lastID: this.lastID });
                    }
                );
            });

            // 为默认管理员分配管理员角色
            await dbAsync('run',
                'INSERT INTO user_roles (user_id, role_id) VALUES (?, ?)',
                [adminResult.lastID, adminRoleId]
            );
        }

        // 添加恢复权限
        const restorePermissionExists = await dbAsync('get', 
            'SELECT 1 FROM permissions WHERE code = ?', 
            ['restore_documents']
        );
        
        if (!restorePermissionExists) {
            await dbAsync('run', 
                `INSERT INTO permissions (code, name, description) 
                 VALUES ('restore_documents', '恢复文档', '允许恢复已删除的文档')`
            );
            
            // 为管理员角色添加恢复权限
            const restorePermId = await dbAsync('get', 
                'SELECT id FROM permissions WHERE code = ?', 
                ['restore_documents']
            );
            
            await dbAsync('run',
                'INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)',
                [1, restorePermId.id]  // 1是管理员角色ID
            );
        }

        console.log('数据库初始化成功');
        console.log('默认管理员账号：admin');
        console.log('默认管理员密码：Bl123456');
    } catch (err) {
        console.error('数据库初始化失败:', err);
        process.exit(1);
    }
};

// 允许注册的用户名白名单
const ALLOWED_USERNAMES = [
    '刘立柱', '杨中威', '王忠志', '李兰霏', '赵会朋', 
    '李树武', '孙铁锋', '姜娜', '王希', '夏丽叶',
    '陈春玉', '王艳秋', '周鹤群', '张启', '刘力',
    '孙莹莹', '黄雪岩', '闫亦燊', '龚子超', '谢双',
    '李思昊', '张彤', '张佳琦', '孙茂勇', '杨磊',
    '王楹凯', '李胜楠', '宋孝成', '陈春生', '关永民',
    '张政海', '王晓洁', '辛琪琪', '郝晓会', '许立国',
    '杨玉杰', '李春雷', '周春浩', '马先有', '翟雪雯',
    '刘树义', '刘兴旺', '姚晓坤', '孙洪波', '邢洪涛',
    '张金龙', '刘芳', '吴志超', '王福明', '王钰清',
    '王江楠', '张兆龙', '张晓军', '李彦秋', '李生',
    '王玮珉', '卢嘉政', '王文县', '于恒奎', '赵德生',
    '田宝东'
];

app.post('/api/login', asyncHandler(async (req, res) => {
    const { username, password } = req.body;
    if (!username || !password) {
        return res.status(400).json({ error: '请输入用户名和密码' });
    }

    const user = await dbAsync('get', 
        'SELECT * FROM users WHERE username = ? AND password = ?',
        [username, password]
    );

    if (!user) {
        return res.status(401).json({ error: '用户名或密码错误' });
    }

    // 获取用户角色和权限
    const roles = await dbAsync('all', `
        SELECT r.* 
        FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ?
    `, [user.id]);

    const permissions = await dbAsync('all', `
        SELECT DISTINCT p.code
        FROM permissions p
        JOIN role_permissions rp ON p.id = rp.permission_id
        JOIN user_roles ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = ?
    `, [user.id]);

    res.json({
        id: user.id,
        username: user.username,
        roles: roles,
        permissions: permissions.map(p => p.code)
    });
}));

app.post('/api/register', asyncHandler(async (req, res) => {
    const { username, password } = req.body;
    
    // 验证用户名
    if (!username || !password) {
        return res.status(400).json({ error: '请输入用户名和密码' });
    }

    // 检查是否在允许的用户名列表中
    if (!ALLOWED_USERNAMES.includes(username)) {
        return res.status(400).json({ error: '该用户名不在允许注册的名单中' });
    }

    if (username.length < 2 || username.length > 16) {
        return res.status(400).json({ error: '用户名长度应为2-16位' });
    }

    // 验证密码
    if (password.length < 8) {
        return res.status(400).json({ error: '密码长度至少8位' });
    }

    if (!/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/.test(password)) {
        return res.status(400).json({ error: '密码必须包含字母和数字' });
    }

    const exists = await dbAsync('get', 
        'SELECT 1 FROM users WHERE username = ?', 
        [username]
    );

    if (exists) {
        return res.status(400).json({ error: '用户名已存在' });
    }

    // 开始事务
    await dbAsync('run', 'BEGIN TRANSACTION');

    try {
        // 插入用户
        const result = await new Promise((resolve, reject) => {
            db.run(
                'INSERT INTO users (username, password) VALUES (?, ?)',
                [username, password],
                function(err) {
                    if (err) reject(err);
                    else resolve({ lastID: this.lastID });
                }
            );
        });

        // 分配普通用户角色
        await dbAsync('run',
            'INSERT INTO user_roles (user_id, role_id) VALUES (?, ?)',
            [result.lastID, 2]  // 2是普通用户角色ID
        );

        await dbAsync('run', 'COMMIT');
        res.json({ message: '注册成功' });
    } catch (error) {
        await dbAsync('run', 'ROLLBACK');
        throw error;
    }
}));

app.post('/api/documents', asyncHandler(async (req, res) => {
    const { type, title } = req.body;
    const userId = req.headers['user-id'];
    
    console.log('创建文档请求:', {
        type,
        title,
        userId,
        headers: req.headers,
        body: req.body
    });

    // 验证用户ID
    if (!userId) {
        console.error('未提供用户ID');
        return res.status(400).json({ error: '未找到用户信息，请重新登录' });
    }

    // 验证用户是否存在
    const user = await dbAsync('get', 'SELECT * FROM users WHERE id = ?', [userId]);
    if (!user) {
        console.error('用户不存在:', userId);
        return res.status(400).json({ error: '用户不存在，请重新登录' });
    }

    const now = new Date();
    const year = now.getFullYear();
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
        try {
            const lockAcquired = await acquireLock(type);
            if (!lockAcquired) {
                await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
                retryCount++;
                continue;
            }

            await dbAsync('run', 'BEGIN TRANSACTION');

            try {
                // 查询当年所有文档类型的号码使用情况（不区分类型）
                const query = `
                    SELECT number
                    FROM documents
                    WHERE strftime('%Y', created_at) = ?
                    ORDER BY number ASC
                `;

                const usedNumbers = await dbAsync('all', query, [year.toString()]);

                // 新的一年，从1开始编号
                let nextNumber = 1;

                if (usedNumbers.length > 0) {
                    const numbers = usedNumbers.map(row => row.number);
                    // 查找当年最小的未使用号码
                    for (let i = 1; i <= numbers[numbers.length - 1] + 1; i++) {
                        if (!numbers.includes(i)) {
                            nextNumber = i;
                            break;
                        }
                    }
                }

                console.log('准备插入文档:', {
                    type,
                    number: nextNumber,
                    title,
                    year,
                    userId: parseInt(userId)
                });

                const result = await new Promise((resolve, reject) => {
                    db.run(
                        'INSERT INTO documents (type, number, title, created_at, year, user_id) VALUES (?, ?, ?, datetime("now", "localtime"), ?, ?)',
                        [type, nextNumber, title, year, parseInt(userId)],
                        function(err) {
                            if (err) {
                                console.error('插入文档失败:', err);
                                reject(err);
                            } else {
                                console.log('插入文档成功, ID:', this.lastID);
                                resolve({ lastID: this.lastID });
                            }
                        }
                    );
                });

                await dbAsync('run', 'COMMIT');
                await releaseLock(type);

                // 使用原始号码，不补零
                const formattedNumber = `${type}〔${year}〕${nextNumber}号`;
                
                const response = {
                    id: result.lastID,
                    number: formattedNumber,
                    year: year,
                    sequence: nextNumber
                };
                
                // 数据变更后触发备份
                backupDatabase().then(() => {
                    console.log('取号后自动备份完成');
                }).catch(err => {
                    console.error('取号后自动备份失败:', err);
                });
                
                console.log('创建成功:', response);
                return res.json(response);

            } catch (error) {
                console.error('创建文档事务失败:', error);
                await dbAsync('run', 'ROLLBACK');
                await releaseLock(type);
                throw error;
            }

        } catch (error) {
            if (retryCount === maxRetries - 1) {
                console.error('取号失败:', error);
                return res.status(500).json({ 
                    error: '系统繁忙，请稍后重试',
                    details: error.message 
                });
            }
            retryCount++;
        }
    }
}));

// 获取查询选项API（必须在 /api/documents 之前）
app.get('/api/documents/search-options', asyncHandler(async (req, res) => {
    const userId = req.headers['user-id'];

    if (!userId) {
        return res.status(401).json({ error: '请先登录' });
    }

    // 获取用户角色
    const userRoles = await dbAsync('all', `
        SELECT r.name
        FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ?
    `, [userId]);

    const isAdmin = userRoles.some(role => role.name === 'admin');

    // 权限控制：非管理员只能看到自己的数据选项
    const userFilter = isAdmin ? '' : 'WHERE d.user_id = ?';
    const userParams = isAdmin ? [] : [userId];

    // 获取所有年份
    const yearsResult = await dbAsync('all', `
        SELECT DISTINCT year
        FROM documents d
        ${userFilter}
        ORDER BY year DESC
    `, userParams);
    const years = yearsResult.map(row => row.year);

    // 获取所有创建者（管理员可以看到所有用户，普通用户只能看到自己）
    let creatorsResult;
    if (isAdmin) {
        creatorsResult = await dbAsync('all', `
            SELECT DISTINCT u.id, u.username
            FROM users u
            INNER JOIN documents d ON u.id = d.user_id
            ORDER BY u.username
        `);
    } else {
        creatorsResult = await dbAsync('all', `
            SELECT id, username
            FROM users
            WHERE id = ?
        `, [userId]);
    }

    res.json({
        years: years,
        creators: creatorsResult
    });
}));

// 文档查询API（必须在 /api/documents 之前）
app.get('/api/documents/search', asyncHandler(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const userId = req.headers['user-id'];

    if (!userId) {
        return res.status(401).json({ error: '请先登录' });
    }

    const { type, year, creator, startDate, endDate, number, keyword } = req.query;

    // 获取用户角色
    const userRoles = await dbAsync('all', `
        SELECT r.name
        FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ?
    `, [userId]);

    const isAdmin = userRoles.some(role => role.name === 'admin');

    // 构建查询条件
    let whereConditions = [];
    let queryParams = [];

    // 权限控制：非管理员只能查看自己的文档
    if (!isAdmin) {
        whereConditions.push('d.user_id = ?');
        queryParams.push(userId);
    }

    if (type) {
        whereConditions.push('d.type = ?');
        queryParams.push(type);
    }

    if (year) {
        whereConditions.push('d.year = ?');
        queryParams.push(parseInt(year));
    }

    if (creator) {
        whereConditions.push('d.user_id = ?');
        queryParams.push(parseInt(creator));
    }

    if (startDate) {
        whereConditions.push('DATE(d.created_at) >= ?');
        queryParams.push(startDate);
    }

    if (endDate) {
        whereConditions.push('DATE(d.created_at) <= ?');
        queryParams.push(endDate);
    }

    if (number) {
        // 支持单个号码、范围查询（如：1-5）、多个号码（如：1,3,5）
        const numberConditions = [];
        const numberParts = number.split(',').map(part => part.trim());

        for (const part of numberParts) {
            if (part.includes('-')) {
                // 范围查询
                const [start, end] = part.split('-').map(n => parseInt(n.trim()));
                if (!isNaN(start) && !isNaN(end)) {
                    numberConditions.push('(d.number >= ? AND d.number <= ?)');
                    queryParams.push(start, end);
                }
            } else {
                // 单个号码
                const num = parseInt(part);
                if (!isNaN(num)) {
                    numberConditions.push('d.number = ?');
                    queryParams.push(num);
                }
            }
        }

        if (numberConditions.length > 0) {
            whereConditions.push(`(${numberConditions.join(' OR ')})`);
        }
    }

    if (keyword) {
        whereConditions.push('d.title LIKE ?');
        queryParams.push(`%${keyword}%`);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // 获取查询结果总数
    const countQuery = `
        SELECT COUNT(*) as total
        FROM documents d
        LEFT JOIN users u ON d.user_id = u.id
        ${whereClause}
    `;
    const countResult = await dbAsync('get', countQuery, queryParams);
    const total = countResult.total;
    const totalPages = Math.ceil(total / limit);

    // 获取查询结果
    const searchQuery = `
        SELECT d.*, u.username as creator_name
        FROM documents d
        LEFT JOIN users u ON d.user_id = u.id
        ${whereClause}
        ORDER BY d.year DESC, d.created_at DESC
        LIMIT ? OFFSET ?
    `;

    const documents = await dbAsync('all', searchQuery, [...queryParams, limit, offset]);

    // 获取每个文档的附件
    const docsWithAttachments = await Promise.all(documents.map(async doc => {
        const attachments = await dbAsync('all', `
            SELECT id, filename, filepath, file_type, size
            FROM attachments
            WHERE document_id = ?
            ORDER BY created_at DESC
        `, [doc.id]);

        const processedAttachments = attachments.map(att => ({
            ...att,
            filename: att.filename.toString('utf8'),
            size: att.size || 0
        }));

        return {
            ...doc,
            formatted_number: `${doc.type}〔${doc.year}〕${doc.number}号`,
            attachments: processedAttachments || []
        };
    }));

    res.json({
        documents: docsWithAttachments,
        total: total,
        pagination: {
            currentPage: page,
            totalPages: totalPages,
            totalItems: total
        }
    });
}));

app.get('/api/documents', asyncHandler(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const userId = req.headers['user-id'];

    if (!userId) {
        return res.status(401).json({ error: '请先登录' });
    }

    // 获取用户角色
    const userRoles = await dbAsync('all', `
        SELECT r.name
        FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ?
    `, [userId]);

    const isAdmin = userRoles.some(role => role.name === 'admin');

    // 根据用户角色构建查询条件
    const whereClause = isAdmin ? '' : 'WHERE d.user_id = ?';
    const queryParams = isAdmin ? [limit, offset] : [userId, limit, offset];

    const [documents, countResult] = await Promise.all([
        dbAsync('all', `
            SELECT
                d.*,
                u.username as creator_name
            FROM documents d
            LEFT JOIN users u ON d.user_id = u.id
            ${whereClause}
            ORDER BY d.year DESC, d.created_at DESC
            LIMIT ? OFFSET ?
        `, queryParams),
        dbAsync('get', `
            SELECT COUNT(*) as total
            FROM documents d
            ${whereClause}
        `, isAdmin ? [] : [userId])
    ]);

    // 获取每个文档的附件
    const docsWithAttachments = await Promise.all(documents.map(async doc => {
        const attachments = await dbAsync('all', `
            SELECT id, filename, filepath, file_type, size
            FROM attachments
            WHERE document_id = ?
            ORDER BY created_at DESC
        `, [doc.id]);

        // 确保所有文件名都是UTF-8编码，并且文件大小存在
        const processedAttachments = attachments.map(att => {
            // 如果size不存在，尝试从文件系统获取
            if (!att.size && fs.existsSync(att.filepath)) {
                const stats = fs.statSync(att.filepath);
                att.size = stats.size;

                // 更新数据库中的文件大小
                db.run('UPDATE attachments SET size = ? WHERE id = ?', [stats.size, att.id]);
            }

            return {
                ...att,
                filename: att.filename.toString('utf8'),
                size: att.size || 0
            };
        });

        return {
            ...doc,
            formatted_number: `${doc.type}〔${doc.year}〕${doc.number}号`,
            attachments: processedAttachments || []
        };
    }));

    res.json({
        records: docsWithAttachments,
        total: countResult.total,
        page,
        totalPages: Math.ceil(countResult.total / limit)
    });
}));



app.post('/api/documents/:id/attachments', upload.array('files'), asyncHandler(async (req, res) => {
    const documentId = req.params.id;
    const files = req.files;

    const attachments = await Promise.all(files.map(async file => {
        // 解码原始文件名
        const originalName = Buffer.from(file.originalname, 'latin1').toString('utf8');
        
        // 获取文件大小
        const stats = fs.statSync(file.path);
        
        // 使用 Promise 包装 db.run 以获取 lastID
        const result = await new Promise((resolve, reject) => {
            db.run(
                'INSERT INTO attachments (document_id, filename, filepath, file_type, size) VALUES (?, ?, ?, ?, ?)',
                [documentId, originalName, file.path, getFileType(originalName), stats.size],
                function(err) {
                    if (err) reject(err);
                    else resolve({ lastID: this.lastID });
                }
            );
        });

        return {
            id: result.lastID,
            filename: originalName,
            filepath: file.path,
            file_type: getFileType(originalName),
            size: stats.size
        };
    }));

    // 上传附件后触发备份
    backupDatabase().then(() => {
        console.log('上传附件后自动备份完成');
    }).catch(err => {
        console.error('上传附件后自动备份失败:', err);
    });

    res.json({ 
        message: '文件上传成功',
        attachments 
    });
}));

app.get('/api/attachments/:id/info', asyncHandler(async (req, res) => {
    const userId = req.headers['user-id'];
    
    if (!userId) {
        return res.status(401).json({ error: '请先登录' });
    }

    // 获取附件信息，同时获取文档创建者信息
    const attachment = await dbAsync('get', `
        SELECT a.*, d.user_id as document_creator_id
        FROM attachments a
        JOIN documents d ON a.document_id = d.id
        WHERE a.id = ?
    `, [req.params.id]);

    if (!attachment) {
        return res.status(404).json({ error: '附件不存在' });
    }

    // 检查用户权限
    const userRoles = await dbAsync('all', `
        SELECT r.name 
        FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ?
    `, [userId]);

    const isAdmin = userRoles.some(role => role.name === 'admin');

    // 如果不是管理员，检查是否是文档创建者
    if (!isAdmin && attachment.document_creator_id !== parseInt(userId)) {
        return res.status(403).json({ error: '您没有权限查看此附件' });
    }

    // 确保文件名是UTF-8编码
    attachment.filename = attachment.filename.toString('utf8');
    
    res.json(attachment);
}));

app.delete('/api/documents/:id', asyncHandler(async (req, res) => {
    const documentId = req.params.id;
    const userId = req.headers['user-id'];

    // 检查文档是否存在
    const document = await dbAsync('get', `
        SELECT * FROM documents 
        WHERE id = ?
    `, [documentId]);

    if (!document) {
        return res.status(404).json({ error: '文档不存在' });
    }

    // 检查用户权限
    const userRoles = await dbAsync('all', `
        SELECT r.name 
        FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ?
    `, [userId]);

    const isAdmin = userRoles.some(role => role.name === 'admin');

    // 如果不是管理员，检查是否是文档创建者
    if (!isAdmin && document.user_id !== parseInt(userId)) {
        return res.status(403).json({ error: '您只能删除自己创建的文档' });
    }

    // 开始事务
    await dbAsync('run', 'BEGIN TRANSACTION');

    try {
        // 删除文档的所有附件
        const attachments = await dbAsync('all', 'SELECT filepath FROM attachments WHERE document_id = ?', [documentId]);
        
        // 删除附件文件
        for (const attachment of attachments) {
            if (fs.existsSync(attachment.filepath)) {
                fs.unlinkSync(attachment.filepath);
            }
        }

        // 删除附件记录
        await dbAsync('run', 'DELETE FROM attachments WHERE document_id = ?', [documentId]);
        
        // 删除文档历史记录
        await dbAsync('run', 'DELETE FROM document_history WHERE document_id = ?', [documentId]);

        // 删除文档
        await dbAsync('run', 'DELETE FROM documents WHERE id = ?', [documentId]);

        await dbAsync('run', 'COMMIT');

        // 数据变更后触发备份
        backupDatabase().then(() => {
            console.log('删除文档后自动备份完成');
        }).catch(err => {
            console.error('删除文档后自动备份失败:', err);
        });

    res.json({ message: '文档删除成功' });
    } catch (error) {
        await dbAsync('run', 'ROLLBACK');
        throw error;
    }
}));

// 导出Excel API
app.get('/api/export', checkPermission('export_excel'), asyncHandler(async (req, res) => {
    const data = await dbAsync('all', `
        SELECT 
            d.id,
            d.type,
            d.number,
            d.year,
            d.title,
            d.created_at,
            u.username as creator
        FROM documents d
        LEFT JOIN users u ON d.user_id = u.id
        WHERE d.deleted_at IS NULL
        ORDER BY d.created_at DESC
    `);
    
    res.json(data);
}));

// 预览附件API（用于iframe，支持token认证）
app.get('/api/attachments/:id/preview', asyncHandler(async (req, res) => {
    const userId = req.headers['user-id'] || req.query.userId;
    
    if (!userId) {
        return res.status(401).json({ error: '请先登录' });
    }

    // 检查用户权限
    const permissions = await dbAsync('all', `
        SELECT DISTINCT p.code
        FROM permissions p
        JOIN role_permissions rp ON p.id = rp.permission_id
        JOIN user_roles ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = ?
    `, [userId]);

    const userPermissions = permissions.map(p => p.code);
    
    if (!userPermissions.includes('download_attachments')) {
        return res.status(403).json({ error: '没有预览权限' });
    }

    const attachment = await dbAsync('get', 
        'SELECT * FROM attachments WHERE id = ?', 
        [req.params.id]
    );

    if (!attachment) {
        return res.status(404).json({ error: '附件不存在' });
    }

    // 设置正确的Content-Type
    const ext = path.extname(attachment.filename).toLowerCase();
    if (ext === '.pdf') {
        res.setHeader('Content-Type', 'application/pdf');
    } else if (['.jpg', '.jpeg', '.png', '.gif'].includes(ext)) {
        res.setHeader('Content-Type', `image/${ext.slice(1)}`);
    }

    // 直接发送文件内容
    res.sendFile(path.resolve(attachment.filepath));
}));

// 下载附件API
app.get('/api/attachments/:id', checkPermission('download_attachments'), asyncHandler(async (req, res) => {
    const attachment = await dbAsync('get', 
        'SELECT * FROM attachments WHERE id = ?', 
        [req.params.id]
    );

    if (!attachment) {
        return res.status(404).json({ error: '附件不存在' });
    }

    // 设置响应头 - 使用RFC 5987标准来处理中文文件名
    const encodedFilename = encodeURIComponent(attachment.filename);
    res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodedFilename}`);
    res.setHeader('Content-Type', 'application/octet-stream');

    // 发送文件
    const fileStream = fs.createReadStream(attachment.filepath);
    fileStream.pipe(res);
}));

// 下载所有附件API
app.get('/api/attachments', checkPermission('download_attachments'), asyncHandler(async (req, res) => {
    const attachments = await dbAsync('all', `
        SELECT a.*, d.type, d.number, d.year
        FROM attachments a
        JOIN documents d ON a.document_id = d.id
        ORDER BY a.created_at DESC
    `);

    res.json(attachments);
}));

// 修改密码API
app.post('/api/change-password', asyncHandler(async (req, res) => {
    console.log('收到修改密码请求:', req.body);
    const { userId, oldPassword, newPassword } = req.body;
    
    if (!userId || !oldPassword || !newPassword) {
        console.log('请求参数不完整');
        return res.status(400).json({ error: '请填写完整信息' });
    }

    // 验证旧密码
    console.log('验证旧密码...');
    const user = await dbAsync('get', 
        'SELECT * FROM users WHERE id = ? AND password = ?',
        [userId, oldPassword]
    );

    if (!user) {
        console.log('原密码验证失败');
        return res.status(401).json({ error: '原密码错误' });
    }

    // 验证新密码格式
    if (newPassword.length < 8) {
        console.log('新密码长度不足');
        return res.status(400).json({ error: '新密码长度不能少于8位' });
    }

    if (!/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/.test(newPassword)) {
        console.log('新密码格式不符合要求');
        return res.status(400).json({ error: '新密码必须包含字母和数字' });
    }

    if (oldPassword === newPassword) {
        console.log('新密码与原密码相同');
        return res.status(400).json({ error: '新密码不能与原密码相同' });
    }

    try {
        console.log('开始更新密码...');
        // 更新密码
        await dbAsync('run',
            'UPDATE users SET password = ? WHERE id = ?',
            [newPassword, userId]
        );

        console.log('密码更新成功');
        res.json({ message: '密码修改成功！' });
    } catch (error) {
        console.error('更新密码失败:', error);
        res.status(500).json({ error: '修改密码失败，请重试' });
    }
}));

// 管理员注销用户账号API
app.post('/api/deactivate-user', asyncHandler(async (req, res) => {
    console.log('收到注销用户请求:', req.body);
    const { targetUserId } = req.body;
    const adminUserId = req.headers['user-id'];
    
    if (!targetUserId) {
        return res.status(400).json({ error: '请指定要注销的用户' });
    }

    // 验证当前用户是否为管理员
    const adminRoles = await dbAsync('all', `
        SELECT r.name 
        FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ?
    `, [adminUserId]);

    const isAdmin = adminRoles.some(role => role.name === 'admin');
    
    if (!isAdmin) {
        return res.status(403).json({ error: '只有管理员可以注销用户账号' });
    }

    // 验证目标用户是否存在
    const targetUser = await dbAsync('get', 
        'SELECT * FROM users WHERE id = ?',
        [targetUserId]
    );

    if (!targetUser) {
        return res.status(404).json({ error: '用户不存在' });
    }

    // 不能注销自己
    if (parseInt(targetUserId) === parseInt(adminUserId)) {
        return res.status(400).json({ error: '不能注销自己的账号' });
    }

    // 不能注销其他管理员
    const targetUserRoles = await dbAsync('all', `
        SELECT r.name 
        FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ?
    `, [targetUserId]);

    const isTargetAdmin = targetUserRoles.some(role => role.name === 'admin');
    if (isTargetAdmin) {
        return res.status(400).json({ error: '不能注销其他管理员账号' });
    }

    try {
        await dbAsync('run', 'BEGIN TRANSACTION');

        // 检查用户是否有历史记录
        const historyCount = await dbAsync('get', 
            'SELECT COUNT(*) as count FROM document_history WHERE modified_by = ?', 
            [targetUserId]
        );

        console.log(`用户 ${targetUser.username} 有 ${historyCount.count} 条历史记录`);

        // 删除用户角色关联
        await dbAsync('run', 'DELETE FROM user_roles WHERE user_id = ?', [targetUserId]);

        // 删除用户账号
        await dbAsync('run', 'DELETE FROM users WHERE id = ?', [targetUserId]);

        await dbAsync('run', 'COMMIT');

        console.log(`管理员 ${adminUserId} 注销了用户 ${targetUser.username} 的账号`);
        
        let message = `用户 ${targetUser.username} 的账号已注销！`;
        if (historyCount.count > 0) {
            message += `\n注意：该用户的 ${historyCount.count} 条历史记录将显示为"已注销用户"。`;
        }
        
        res.json({ 
            message: message,
            username: targetUser.username,
            historyCount: historyCount.count
        });
    } catch (error) {
        await dbAsync('run', 'ROLLBACK');
        console.error('注销用户失败:', error);
        res.status(500).json({ error: '注销用户失败，请重试' });
    }
}));

// 模板管理API

// 获取模板列表
app.get('/api/templates', asyncHandler(async (req, res) => {
    const templates = await dbAsync('all', `
        SELECT 
            t.*,
            COALESCE(u.username, '已注销用户') as uploader_name
        FROM templates t
        LEFT JOIN users u ON t.uploaded_by = u.id
        ORDER BY t.created_at DESC
    `);

    // 处理文件名编码和大小
    const processedTemplates = templates.map(template => ({
        ...template,
        filename: template.filename.toString('utf8'),
        size: template.size || 0,
        file_type_name: getFileTypeName(template.file_type)
    }));

    res.json(processedTemplates);
}));

// 上传模板（仅管理员）
app.post('/api/templates', templateUpload.single('template'), asyncHandler(async (req, res) => {
    const adminUserId = req.headers['user-id'];
    const { name, description } = req.body;
    const file = req.file;

    if (!adminUserId) {
        return res.status(401).json({ error: '请先登录' });
    }

    // 验证当前用户是否为管理员
    const adminRoles = await dbAsync('all', `
        SELECT r.name 
        FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ?
    `, [adminUserId]);

    const isAdmin = adminRoles.some(role => role.name === 'admin');
    
    if (!isAdmin) {
        // 如果不是管理员，删除已上传的文件
        if (file && fs.existsSync(file.path)) {
            fs.unlinkSync(file.path);
        }
        return res.status(403).json({ error: '只有管理员可以上传模板' });
    }

    if (!file) {
        return res.status(400).json({ error: '请选择要上传的模板文件' });
    }

    if (!name || !name.trim()) {
        // 删除已上传的文件
        if (fs.existsSync(file.path)) {
            fs.unlinkSync(file.path);
        }
        return res.status(400).json({ error: '请输入模板名称' });
    }

    try {
        // 解码原始文件名
        const originalName = Buffer.from(file.originalname, 'latin1').toString('utf8');
        
        // 获取文件大小
        const stats = fs.statSync(file.path);

        // 保存模板信息到数据库
        const result = await new Promise((resolve, reject) => {
            db.run(
                'INSERT INTO templates (name, description, filename, filepath, file_type, size, uploaded_by) VALUES (?, ?, ?, ?, ?, ?, ?)',
                [name.trim(), description?.trim() || '', originalName, file.path, getFileType(originalName), stats.size, adminUserId],
                function(err) {
                    if (err) reject(err);
                    else resolve({ lastID: this.lastID });
                }
            );
        });

        console.log(`管理员 ${adminUserId} 上传了模板: ${name}`);

        res.json({
            message: '模板上传成功！',
            template: {
                id: result.lastID,
                name: name.trim(),
                description: description?.trim() || '',
                filename: originalName,
                file_type: getFileType(originalName),
                size: stats.size
            }
        });
    } catch (error) {
        // 发生错误时删除已上传的文件
        if (file && fs.existsSync(file.path)) {
            fs.unlinkSync(file.path);
        }
        console.error('上传模板失败:', error);
        res.status(500).json({ error: '上传模板失败，请重试' });
    }
}));

// 下载模板
app.get('/api/templates/:id/download', asyncHandler(async (req, res) => {
    const templateId = req.params.id;
    const userId = req.headers['user-id'];

    if (!userId) {
        return res.status(401).json({ error: '请先登录' });
    }

    // 获取模板信息
    const template = await dbAsync('get', 'SELECT * FROM templates WHERE id = ?', [templateId]);

    if (!template) {
        return res.status(404).json({ error: '模板不存在' });
    }

    // 检查文件是否存在
    if (!fs.existsSync(template.filepath)) {
        return res.status(404).json({ error: '模板文件不存在' });
    }

    try {
        // 设置响应头 - 使用RFC 5987标准来处理中文文件名
        const encodedFilename = encodeURIComponent(template.filename);
        res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodedFilename}`);
        res.setHeader('Content-Type', 'application/octet-stream');

        // 发送文件
        const fileStream = fs.createReadStream(template.filepath);
        
        // 处理文件流错误
        fileStream.on('error', (error) => {
            console.error('文件流错误:', error);
            if (!res.headersSent) {
                res.status(500).json({ error: '文件读取失败' });
            }
        });

        fileStream.pipe(res);

        console.log(`用户 ${userId} 下载了模板: ${template.name} (文件名: ${template.filename})`);
    } catch (error) {
        console.error('下载模板失败:', error);
        if (!res.headersSent) {
            res.status(500).json({ error: '下载失败，请重试' });
        }
    }
}));

// 删除模板（仅管理员）
app.delete('/api/templates/:id', asyncHandler(async (req, res) => {
    const templateId = req.params.id;
    const adminUserId = req.headers['user-id'];

    if (!adminUserId) {
        return res.status(401).json({ error: '请先登录' });
    }

    // 验证当前用户是否为管理员
    const adminRoles = await dbAsync('all', `
        SELECT r.name 
        FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ?
    `, [adminUserId]);

    const isAdmin = adminRoles.some(role => role.name === 'admin');
    
    if (!isAdmin) {
        return res.status(403).json({ error: '只有管理员可以删除模板' });
    }

    // 获取模板信息
    const template = await dbAsync('get', 'SELECT * FROM templates WHERE id = ?', [templateId]);

    if (!template) {
        return res.status(404).json({ error: '模板不存在' });
    }

    try {
        await dbAsync('run', 'BEGIN TRANSACTION');

        // 删除文件
        if (fs.existsSync(template.filepath)) {
            fs.unlinkSync(template.filepath);
        }

        // 从数据库中删除记录
        await dbAsync('run', 'DELETE FROM templates WHERE id = ?', [templateId]);

        await dbAsync('run', 'COMMIT');

        console.log(`管理员 ${adminUserId} 删除了模板: ${template.name}`);

        res.json({ 
            message: '模板删除成功！',
            templateName: template.name
        });
    } catch (error) {
        await dbAsync('run', 'ROLLBACK');
        console.error('删除模板失败:', error);
        res.status(500).json({ error: '删除模板失败，请重试' });
    }
}));

// 获取所有用户列表（仅管理员）
app.get('/api/users', asyncHandler(async (req, res) => {
    const adminUserId = req.headers['user-id'];
    
    // 验证当前用户是否为管理员
    const adminRoles = await dbAsync('all', `
        SELECT r.name 
        FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ?
    `, [adminUserId]);

    const isAdmin = adminRoles.some(role => role.name === 'admin');
    
    if (!isAdmin) {
        return res.status(403).json({ error: '只有管理员可以查看用户列表' });
    }

    const users = await dbAsync('all', `
        SELECT 
            u.id,
            u.username,
            u.created_at,
            GROUP_CONCAT(r.name) as roles
        FROM users u
        LEFT JOIN user_roles ur ON u.id = ur.user_id
        LEFT JOIN roles r ON ur.role_id = r.id
        GROUP BY u.id, u.username, u.created_at
        ORDER BY u.created_at DESC
    `);

    res.json(users);
}));

// 添加ping接口
app.get('/api/ping', (req, res) => {
    res.json({ status: 'ok' });
});

// 获取备份列表
app.get('/api/backups', checkPermission('restore_documents'), asyncHandler(async (req, res) => {
    const backups = await dbAsync('all', `
        SELECT * FROM backups 
        ORDER BY created_at DESC
    `);
    res.json(backups);
}));

// 修改文档标题
app.put('/api/documents/:id/title', asyncHandler(async (req, res) => {
    const documentId = req.params.id;
    const { title } = req.body;
    const userId = req.headers['user-id'];

    // 验证参数
    if (!title || !title.trim()) {
        return res.status(400).json({ error: '标题不能为空' });
    }

    // 检查文档是否存在
    const document = await dbAsync('get', `
        SELECT * FROM documents 
        WHERE id = ?
    `, [documentId]);

    if (!document) {
        return res.status(404).json({ error: '文档不存在' });
    }

    // 检查用户权限
    const userRoles = await dbAsync('all', `
        SELECT r.name 
        FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ?
    `, [userId]);

    const isAdmin = userRoles.some(role => role.name === 'admin');

    // 如果不是管理员，检查是否是文档创建者
    if (!isAdmin && document.user_id !== parseInt(userId)) {
        return res.status(403).json({ error: '您只能修改自己创建的文档' });
    }

    try {
        await dbAsync('run', 'BEGIN TRANSACTION');

        // 更新文档标题
        await dbAsync('run', `
            UPDATE documents 
            SET title = ?
            WHERE id = ?
        `, [title, documentId]);

        await dbAsync('run', 'COMMIT');

        // 数据变更后触发备份
        backupDatabase().then(() => {
            console.log('修改标题后自动备份完成');
        }).catch(err => {
            console.error('修改标题后自动备份失败:', err);
        });

        res.json({ 
            message: '标题修改成功',
            oldTitle: document.title,
            newTitle: title
        });
    } catch (error) {
        await dbAsync('run', 'ROLLBACK');
        console.error('修改标题失败:', error);
        res.status(500).json({ error: '修改标题失败，请重试' });
    }
}));

// 获取文档修改历史
app.get('/api/documents/:id/history', asyncHandler(async (req, res) => {
    const documentId = req.params.id;
    
    const history = await dbAsync('all', `
        SELECT 
            h.*,
            COALESCE(u.username, '已注销用户') as modifier_name,
            d.title as document_title
        FROM document_history h
        LEFT JOIN users u ON h.modified_by = u.id
        JOIN documents d ON h.document_id = d.id
        WHERE h.document_id = ?
        ORDER BY h.modified_at DESC
    `, [documentId]);

    res.json(history);
}));

// 删除附件
app.delete('/api/attachments/:id', asyncHandler(async (req, res) => {
    const attachmentId = req.params.id;
    const userId = req.headers['user-id'];

    // 获取附件信息
    const attachment = await dbAsync('get', `
        SELECT a.*, d.user_id as document_creator_id
        FROM attachments a
        JOIN documents d ON a.document_id = d.id
        WHERE a.id = ?
    `, [attachmentId]);

    if (!attachment) {
        return res.status(404).json({ error: '附件不存在' });
    }

    // 检查用户权限
    const userRoles = await dbAsync('all', `
        SELECT r.name 
        FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ?
    `, [userId]);

    const isAdmin = userRoles.some(role => role.name === 'admin');

    // 如果不是管理员，检查是否是文档创建者
    if (!isAdmin && attachment.document_creator_id !== parseInt(userId)) {
        return res.status(403).json({ error: '您只能删除自己文档的附件' });
    }

    try {
        await dbAsync('run', 'BEGIN TRANSACTION');

        // 删除文件
        if (fs.existsSync(attachment.filepath)) {
            fs.unlinkSync(attachment.filepath);
        }

        // 从数据库中删除记录
        await dbAsync('run', 'DELETE FROM attachments WHERE id = ?', [attachmentId]);

        await dbAsync('run', 'COMMIT');

        // 删除附件后触发备份
        backupDatabase().then(() => {
            console.log('删除附件后自动备份完成');
        }).catch(err => {
            console.error('删除附件后自动备份失败:', err);
        });

        res.json({ message: '附件删除成功' });
    } catch (error) {
        await dbAsync('run', 'ROLLBACK');
        console.error('删除附件失败:', error);
        res.status(500).json({ error: '删除附件失败' });
    }
}));

// 备份相关函数
const backupDir = path.join(__dirname, 'backups');

// 确保备份目录存在
if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
}

// 执行数据库备份
const backupDatabase = () => {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = path.join(__dirname, 'backups', `backup-${timestamp}`);
    
    // 创建带时间戳的备份目录
    if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
    }
    
    const backupFile = path.join(backupDir, 'documents.db');
    
    return new Promise((resolve, reject) => {
        const backup = db.backup(backupFile);
        
        backup.step(-1, async (err) => {
            if (err) {
                console.error('备份过程出错:', err);
                backup.finish();
                reject(err);
                return;
            }
            
            // 备份附件文件
            try {
                const uploadsDir = path.join(__dirname, 'uploads');
                const backupUploadsDir = path.join(backupDir, 'uploads');
                
                // 创建备份的uploads目录
                if (!fs.existsSync(backupUploadsDir)) {
                    fs.mkdirSync(backupUploadsDir, { recursive: true });
                }
                
                // 复制所有附件文件
                const files = fs.readdirSync(uploadsDir);
                for (const file of files) {
                    const sourcePath = path.join(uploadsDir, file);
                    const targetPath = path.join(backupUploadsDir, file);
                    
                    // 只复制文件，不复制目录
                    if (fs.statSync(sourcePath).isFile()) {
                        fs.copyFileSync(sourcePath, targetPath);
                    }
                }
                
                console.log(`数据库和附件已备份到: ${backupDir}`);
                backup.finish();
                resolve(backupDir);
            } catch (err) {
                console.error('备份附件文件失败:', err);
                backup.finish();
                reject(err);
            }
        });
    });
};

// 清理旧备份
const cleanOldBackups = () => {
    const maxBackups = 7; // 保留最近7天的备份
    
    fs.readdir(backupDir, (err, files) => {
        if (err) {
            console.error('读取备份目录失败:', err);
            return;
        }
        
        // 按创建时间排序
        const backupDirs = files
            .filter(file => file.startsWith('backup-'))
            .map(file => ({
                name: file,
                path: path.join(backupDir, file),
                time: fs.statSync(path.join(backupDir, file)).mtime.getTime()
            }))
            .sort((a, b) => b.time - a.time);
        
        // 删除多余的备份
        if (backupDirs.length > maxBackups) {
            backupDirs.slice(maxBackups).forEach(dir => {
                // 递归删除备份目录及其内容
                fs.rmSync(dir.path, { recursive: true, force: true }, err => {
                    if (err) {
                        console.error(`删除旧备份失败 ${dir.name}:`, err);
                    } else {
                        console.log(`已删除旧备份: ${dir.name}`);
                    }
                });
            });
        }
    });
};

// 设置定时备份任务
setInterval(async () => {
    try {
        await backupDatabase();
        cleanOldBackups();
    } catch (err) {
        console.error('自动备份失败:', err);
    }
}, 24 * 60 * 60 * 1000); // 每24小时执行一次

// 在服务器启动时执行一次备份
backupDatabase().catch(err => {
    console.error('初始备份失败:', err);
});

// 添加错误处理中间件
app.use((err, req, res, next) => {
    if (err instanceof multer.MulterError) {
        if (err.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({ error: '文件大小超过50MB限制' });
        }
        return res.status(400).json({ error: '文件上传失败：' + err.message });
    }
    if (err) {
        console.error('服务器错误:', err);
        return res.status(500).json({ error: err.message || '服务器内部错误' });
    }
    next();
});

// 尝试启动服务器的函数
const startServer = (port) => {
    return new Promise((resolve, reject) => {
        const server = app.listen(port, '0.0.0.0')  // 修改这里，监听所有网络接口
            .on('error', (err) => {
                if (err.code === 'EADDRINUSE') {
                    console.log(`端口 ${port} 已被占用，尝试下一个端口...`);
                    resolve(false);
                } else {
                    reject(err);
                }
            })
            .on('listening', () => {
                console.log(`服务器已启动，请通过以下地址访问：`);
                console.log(`- 本机访问：http://localhost:${port}`);
                console.log(`- 局域网访问：http://[本机IP]:${port}`);
                resolve(true);
            });
    });
};

// 初始化数据库并启动服务器
initDb().then(async () => {
    const ports = [3000, 3001, 3002, 3003];  // 可能的端口列表
    
    for (const port of ports) {
        try {
            const success = await startServer(port);
            if (success) {
                break;  // 成功启动服务器，退出循环
            }
        } catch (err) {
            console.error(`在端口 ${port} 启动服务器失败:`, err);
            if (port === ports[ports.length - 1]) {
                console.error('所有端口都无法使用，退出程序');
                process.exit(1);
            }
        }
    }
});