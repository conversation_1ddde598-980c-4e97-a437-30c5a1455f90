<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iIzE4OTBmZiIgZD0iTTE0IDJINmMtMS4xIDAtMiAuOS0yIDJ2MTZjMCAxLjEuOSAyIDIgMmgxMmMxLjEgMCAyLS45IDItMlY4bC02LTZ6bTQgMThjMCAuNS0uNSAxLTEgMUg3Yy0uNSAwLTEtLjUtMS0xaDZ2NWg1djEyeiIvPjxwYXRoIGZpbGw9IiMxODkwZmYiIGQ9Ik05IDEyaDZ2MUg5di0xem0wIDNoNnYxSDl2LTF6bTAtM2g2djFIOXYtMXoiLz48L3N2Zz4=" type="image/svg+xml">
<title>公文取号系统</title>
<style>
        :root {
            --primary-color: #1890ff;
            --error-color: #ff4d4f;
            --success-color: #52c41a;
            --border-radius: 4px;
            --box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.5;
            color: #333;
            background: #f0f2f5;
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }

        h1 {
            text-align: center;
            margin-bottom: 2rem;
            color: #1f1f1f;
        }

        .form-group {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-direction: column;
        }

        .form-item {
            position: relative;
        }

        .form-item input {
            width: 100%;
            padding: 0.5rem 1rem;
            border: 1px solid #d9d9d9;
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: all 0.3s;
        }

        .form-item input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
            outline: none;
        }

        .form-item .error {
            color: var(--error-color);
            font-size: 0.85rem;
            margin-top: 0.25rem;
            min-height: 1.2em;
        }

        .form-item .hint {
            color: #666;
            font-size: 0.85rem;
            margin-top: 0.25rem;
        }

        .password-strength {
            margin-top: 0.5rem;
            height: 4px;
            background: #f0f0f0;
            border-radius: 2px;
            overflow: hidden;
            position: relative;
            z-index: 1;
        }

        .password-strength-bar {
            height: 100%;
            width: 0;
            transition: all 0.3s;
            position: relative;
            z-index: 1;
        }

        .strength-weak { width: 33.33%; background: var(--error-color); }
        .strength-medium { width: 66.66%; background: #faad14; }
        .strength-strong { width: 100%; background: var(--success-color); }

        .btn-group {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        input, select, button {
            padding: 0.5rem 1rem;
            border: 1px solid #d9d9d9;
            border-radius: var(--border-radius);
            font-size: 1rem;
        }

        input, select {
            flex: 1;
            min-width: 0;
        }

        button {
            cursor: pointer;
            background: white;
            transition: all 0.3s;
        }

        .primary-btn {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .primary-btn:hover {
            opacity: 0.85;
        }

        .secondary-btn {
            color: var(--primary-color);
        }

        .secondary-btn:hover {
            background: rgba(24,144,255,0.1);
        }

        .logout-btn {
            color: var(--error-color);
            border-color: var(--error-color);
        }

        .logout-btn:hover {
            background: rgba(255,77,79,0.1);
        }

        .user-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #f0f0f0;
        }

        .result-box {
            text-align: center;
            margin: 1rem 0;
            padding: 1rem;
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: var(--border-radius);
            color: var(--success-color);
        }

        .table-container {
            overflow-x: auto;
            margin: 2rem 0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        th {
            background: #fafafa;
            font-weight: 500;
        }

        .upload-area {
            max-width: 300px;  /* 限制最大宽度 */
        }

        .attachments-preview {
            display: flex;
            flex-direction: column;  /* 改为纵向排列 */
            gap: 8px;
            max-height: 200px;  /* 限制最大高度 */
            overflow-y: auto;  /* 超出显示滚动条 */
            padding-right: 5px;  /* 为滚动条预留空间 */
        }

        .attachment-item {
            display: flex;
            align-items: center;
            padding: 8px;
            border: 1px solid #eee;
            border-radius: 4px;
            position: relative;
            background: #fff;
            transition: all 0.3s;
            cursor: pointer;
        }

        .attachment-item:hover {
            border-color: var(--primary-color);
            box-shadow: 0 2px 6px rgba(24,144,255,0.1);
        }

        .attachment-preview {
            width: 32px;
            height: 32px;
            min-width: 32px;  /* 防止被压缩 */
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f5f5f5;
            border-radius: 4px;
            font-size: 20px;
        }

        .attachment-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 4px;
        }

        .attachment-info {
            flex: 1;
            min-width: 0;  /* 允许子元素压缩 */
            padding-right: 24px;  /* 为删除按钮预留空间 */
        }

        .attachment-name {
            font-size: 14px;
            color: #333;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .attachment-type {
            font-size: 12px;
            color: #666;
        }

        .attachment-delete {
            position: absolute;
            top: 50%;
            right: 8px;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            line-height: 20px;
            text-align: center;
            background: rgba(255, 77, 79, 0.1);
            color: #ff4d4f;
            border-radius: 50%;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .attachment-item:hover .attachment-delete {
            opacity: 1;
        }

        .attachment-delete:hover {
            background: rgba(255, 77, 79, 0.2);
        }

        .upload-label {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 32px;
            border: 1px dashed #d9d9d9;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            margin-top: 8px;
            color: #666;
            gap: 8px;  /* 添加图标和文字之间的间距 */
            padding: 8px 16px;  /* 增加内边距 */
        }

        .upload-label .icon {
            font-size: 16px;  /* 调整图标大小 */
            display: flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #f0f0f0;  /* 添加背景色 */
            transition: all 0.3s;
        }

        .upload-label:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .upload-label:hover .icon {
            background-color: rgba(24, 144, 255, 0.1);  /* 悬停时改变图标背景色 */
        }

        /* 自定义滚动条样式 */
        .attachments-preview::-webkit-scrollbar {
            width: 4px;
        }

        .attachments-preview::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 2px;
        }

        .attachments-preview::-webkit-scrollbar-thumb {
            background: #ccc;
            border-radius: 2px;
        }

        .attachments-preview::-webkit-scrollbar-thumb:hover {
            background: #999;
        }

        /* 文件大小显示 */
        .attachment-size {
            font-size: 12px;
            color: #999;
            margin-left: 8px;
        }

        /* 添加文件类型图标的颜色 */
        .file-icon {
            display: inline-block;
            width: 24px;
            height: 24px;
            line-height: 24px;
            text-align: center;
        }

        .file-icon-image { color: #52c41a; }
        .file-icon-pdf { color: #ff4d4f; }
        .file-icon-office { color: #1890ff; }
        .file-icon-other { color: #666; }

        /* 调整表格样式 */
        .table-container table {
            table-layout: fixed;  /* 使用固定布局 */
            width: 100%;
        }

        .table-container th,
        .table-container td {
            padding: 12px 8px;
            vertical-align: middle;
            line-height: 1.5;
            position: relative;
        }

        /* 设置各列宽度 */
        .table-container th:nth-child(1),  /* 时间列 */
        .table-container td:nth-child(1) {
            width: 120px;  /* 减小宽度，因为只显示年月日 */
            white-space: nowrap;
        }

        .table-container th:nth-child(2),  /* 类型列 */
        .table-container td:nth-child(2) {
            width: 100px;
            white-space: nowrap;
        }

        .table-container th:nth-child(3),  /* 公文号列 */
        .table-container td:nth-child(3) {
            width: 180px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .table-container th:nth-child(4),  /* 标题列 */
        .table-container td:nth-child(4) {
            min-width: 200px;
        }

        .table-container th:nth-child(5),  /* 创建者列 */
        .table-container td:nth-child(5) {
            width: 100px;
            white-space: nowrap;
        }

        .table-container th:nth-child(6),  /* 附件列 */
        .table-container td:nth-child(6) {
            width: 300px;
        }

        .table-container th:nth-child(7),  /* 操作列 */
        .table-container td:nth-child(7) {
            width: 80px;
            white-space: nowrap;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 1rem;
            align-items: center;
            margin-top: 1rem;
        }

        .toast {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translate(-50%, -20px);
            padding: 10px 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            border-radius: 4px;
            z-index: 1000;
            opacity: 0;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 768px) {
            .container {
                margin: 0;
                padding: 1rem;
                border-radius: 0;
            }

            .form-group {
                flex-direction: column;
            }

            /* 调整表格在移动端的显示 */
            .table-container {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            .table-container table {
                min-width: 900px; /* 增加最小宽度 */
            }

            .table-container th, 
            .table-container td {
                min-width: unset;
                white-space: normal;
                word-break: break-word;
                padding: 8px 4px; /* 减小内边距 */
                font-size: 14px; /* 稍微减小字体 */
            }

            /* 重新设置各列宽度 */
            .table-container th:nth-child(1),
            .table-container td:nth-child(1) {
                width: 90px;  /* 增加时间列宽度 */
                white-space: nowrap; /* 时间不换行 */
            }

            .table-container th:nth-child(2),
            .table-container td:nth-child(2) {
                width: 90px;  /* 增加类型列宽度 */
                white-space: nowrap; /* 类型不换行 */
            }

            .table-container th:nth-child(3),
            .table-container td:nth-child(3) {
                width: 130px;  /* 公文号列 */
                white-space: nowrap;
            }

            .table-container th:nth-child(4),
            .table-container td:nth-child(4) {
                width: 220px;  /* 标题列 */
            }

            .table-container th:nth-child(5),
            .table-container td:nth-child(5) {
                width: 90px;  /* 创建者列 */
                white-space: nowrap;
            }

            .table-container th:nth-child(6),
            .table-container td:nth-child(6) {
                width: 200px;  /* 附件列 */
            }

            .table-container th:nth-child(7),
            .table-container td:nth-child(7) {
                width: 80px;  /* 操作列 */
                white-space: nowrap;
            }

            /* 优化标题显示 */
            .doc-title {
                max-width: 200px;
                padding-right: 35px;
            }

            /* 优化附件预览区域 */
            .attachments-preview {
                max-width: 180px;
            }

            .attachment-item {
                padding: 4px;
            }

            .attachment-preview {
                width: 24px;
                height: 24px;
                min-width: 24px;
            }

            .attachment-name {
                font-size: 12px;
            }

            .attachment-type {
                font-size: 10px;
            }

            /* 优化时间显示格式 */
            .table-container td:first-child {
                font-size: 13px; /* 时间列字体稍微调小 */
            }
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            max-width: 400px;
            width: 90%;
        }

        .modal h2 {
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .modal .form-item {
            margin-bottom: 1rem;
        }

        .modal .form-item input {
            width: 100%;
            padding: 0.5rem 1rem;
            border: 1px solid #d9d9d9;
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: all 0.3s;
        }

        .modal .form-item input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
            outline: none;
        }

        .modal .form-item .error {
            color: var(--error-color);
            font-size: 0.85rem;
            margin-top: 0.25rem;
            min-height: 1.2em;
        }

        /* 添加图标样式 */
        .icon {
            width: 16px;
            height: 16px;
            vertical-align: middle;
        }
        
        .icon-eye {
            color: #666;
        }
        
        .icon-eye:hover {
            color: var(--primary-color);
        }

        /* 添加删除状态样式 */
        .deleted-row {
            background-color: #fff1f0;
        }
        
        .deleted-info {
            color: #ff4d4f;
            font-size: 0.85rem;
        }

        .icon-btn {
            background: none;
            border: none;
            cursor: pointer;
            padding: 2px 5px;
            margin: 0 2px;
            opacity: 0.7;
        }
        .icon-btn:hover {
            opacity: 1;
        }
        .action-buttons {
            display: inline-block;
            margin-left: 8px;
        }
        .history-list table {
            width: 100%;
            border-collapse: collapse;
        }
        .history-list th, .history-list td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .history-list th {
            background: #fafafa;
        }

        /* 用户管理界面样式 */
        .user-manage-modal {
            max-width: 900px;
            width: 95%;
            max-height: 90vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #f0f0f0;
        }

        .modal-header h2 {
            margin: 0;
            color: #1890ff;
            font-size: 1.5rem;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #999;
            padding: 5px;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            background: #f5f5f5;
            color: #666;
        }

        .user-stats {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .stat-card {
            flex: 1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .stat-card:nth-child(2) {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .stat-card:nth-child(3) {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .user-search {
            margin-bottom: 1rem;
        }

        .user-search input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid #e8e8e8;
            border-radius: 25px;
            font-size: 1rem;
            transition: all 0.3s;
        }

        .user-search input:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 3px rgba(24,144,255,0.1);
            outline: none;
        }

        .user-table-container {
            flex: 1;
            overflow-y: auto;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .user-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        .user-table th {
            background: #fafafa;
            padding: 1rem 0.75rem;
            text-align: left;
            font-weight: 600;
            color: #333;
            border-bottom: 2px solid #e8e8e8;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .user-table td {
            padding: 1rem 0.75rem;
            border-bottom: 1px solid #f0f0f0;
            vertical-align: middle;
        }

        .user-table tr:hover {
            background: #f8f9fa;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .user-details {
            display: flex;
            flex-direction: column;
        }

        .user-name {
            font-weight: 600;
            color: #333;
        }

        .user-badge {
            font-size: 0.75rem;
            color: #666;
            margin-top: 2px;
        }

        .role-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .role-admin {
            background: #fff2e8;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }

        .role-user {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        .action-btn {
            padding: 0.4rem 0.8rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.85rem;
            font-weight: 500;
            transition: all 0.3s;
        }

        .deactivate-btn {
            background: #fff1f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }

        .deactivate-btn:hover {
            background: #ff4d4f;
            color: white;
        }

        .disabled-action {
            color: #bbb;
            font-style: italic;
        }

        .modal-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 1rem;
            border-top: 1px solid #f0f0f0;
        }

        .refresh-btn {
            background: #f0f9ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s;
        }

        .refresh-btn:hover {
            background: #1890ff;
            color: white;
        }

        /* 注销用户模态框样式 */
        .deactivate-modal {
            max-width: 500px;
            width: 90%;
        }

        .warning-section {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            background: #fff2f0;
            border: 1px solid #ffccc7;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .warning-icon {
            font-size: 2rem;
            color: #ff4d4f;
        }

        .warning-content h3 {
            margin: 0 0 0.5rem 0;
            color: #ff4d4f;
            font-size: 1.1rem;
        }

        .warning-content p {
            margin: 0;
            color: #666;
            line-height: 1.5;
        }

        .user-info-section {
            background: #fafafa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .info-item:last-child {
            margin-bottom: 0;
        }

        .info-label {
            font-weight: 600;
            color: #333;
            min-width: 80px;
        }

        .info-value {
            color: #666;
            font-weight: 500;
        }

        .danger-btn {
            background: #ff4d4f;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s;
        }

        .danger-btn:hover {
            background: #d32f2f;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
        }

        .danger-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 查询面板样式 */
        .search-panel {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .search-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 1.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px 8px 0 0;
        }

        .search-header h3 {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .collapse-btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 0.4rem 0.8rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s;
        }

        .collapse-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .search-form {
            padding: 1.5rem;
        }

        .search-row {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .search-item {
            flex: 1;
            min-width: 200px;
            display: flex;
            flex-direction: column;
        }

        .search-item-full {
            flex: 100%;
        }

        .search-item label {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .search-item input,
        .search-item select {
            padding: 0.6rem 0.8rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
            transition: all 0.3s;
        }

        .search-item input:focus,
        .search-item select:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
            outline: none;
        }

        .search-actions {
            display: flex;
            gap: 0.8rem;
            justify-content: flex-start;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #e9ecef;
        }

        .search-actions button {
            padding: 0.6rem 1.2rem;
            font-size: 0.9rem;
        }

        /* 查询结果提示 */
        .search-result-info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 4px;
            padding: 0.8rem 1rem;
            margin-bottom: 1rem;
            color: #1890ff;
            font-size: 0.9rem;
        }

        .search-result-info .clear-search {
            color: #ff4d4f;
            cursor: pointer;
            text-decoration: underline;
            margin-left: 0.5rem;
        }

        /* 统一的操作按钮样式 */
        .form-group.action-buttons {
            display: flex;
            flex-direction: row !important;
            gap: 1rem;
            flex-wrap: nowrap;
            justify-content: center;
            align-items: center;
            margin: 1.5rem 0;
        }

        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 0.6rem;
            width: 140px;
            height: 80px;
            padding: 0.75rem;
            border: 1px solid #e0e6ed;
            border-radius: 12px;
            cursor: pointer;
            font-size: 0.85rem;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }

        .primary-action,
        .secondary-action {
            background: #f8f9fa;
            color: #495057;
            border: 1px solid #e0e6ed;
        }

        .primary-action:hover,
        .secondary-action:hover {
            background: #e9ecef;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-color: #ced4da;
        }

        .action-btn:active {
            transform: translateY(0);
        }

        .action-btn:disabled {
            background: #6c757d;
            color: #fff;
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .btn-icon {
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1;
            margin: 0;
        }

        .btn-text {
            font-weight: 500;
            font-size: 0.75rem;
            text-align: center;
            line-height: 1.2;
            margin: 0;
            white-space: nowrap;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 16px;
        }

        /* 按钮光效动画 */
        .action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.6s;
        }

        .action-btn:hover::before {
            left: 100%;
        }

        /* 中等屏幕尺寸优化 - 扩大范围覆盖929-1037px */
        @media (max-width: 1040px) and (min-width: 769px) {
            .table-container {
                overflow-x: auto;
            }

            .table-container table {
                min-width: 950px;
            }

            /* 调整列宽避免重合 */
            .table-container th:nth-child(4),  /* 标题列 */
            .table-container td:nth-child(4) {
                width: 200px;
                min-width: 200px;
            }

            .table-container th:nth-child(5),  /* 创建者列 */
            .table-container td:nth-child(5) {
                width: 100px;
                min-width: 100px;
            }

            .table-container th:nth-child(6),  /* 附件列 */
            .table-container td:nth-child(6) {
                width: 280px;
                min-width: 280px;
            }

            /* 优化标题和图标布局 */
            .doc-title {
                max-width: 150px;
                padding-right: 35px;
            }

            .doc-title-wrapper {
                gap: 6px;
            }

            .action-buttons {
                margin-left: 4px;
                flex-shrink: 0;
            }

            .icon-btn {
                padding: 1px 3px;
                margin: 0 1px;
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .search-row {
                flex-direction: column;
            }

            .search-item {
                min-width: auto;
            }

            .search-actions {
                flex-direction: column;
            }

            .search-actions button {
                width: 100%;
            }

            .form-group.action-buttons {
                flex-wrap: wrap;
                gap: 0.75rem;
                justify-content: center;
            }

            .action-btn {
                width: 120px;
                height: 70px;
                font-size: 0.75rem;
            }

            .btn-icon {
                font-size: 1.2rem;
            }

            .btn-text {
                font-size: 0.7rem;
            }
        }

        /* 添加附件预览相关样式 */
        .preview-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .preview-content {
            max-width: 90%;
            max-height: 90%;
            background: white;
            padding: 20px;
            border-radius: 8px;
            position: relative;
        }

        .preview-close {
            position: absolute;
            top: 10px;
            right: 10px;
            cursor: pointer;
            font-size: 24px;
            color: #666;
        }

        .preview-image {
            max-width: 100%;
            max-height: calc(90vh - 40px);
        }

        .preview-pdf {
            width: 100%;
            height: calc(90vh - 40px);
            border: none;
        }

        .preview-office {
            width: 100%;
            height: calc(90vh - 40px);
            border: none;
        }

        .file-info {
            margin: 15px 0;
        }
        
        .file-name {
            font-size: 16px;
            color: #333;
            word-break: break-all;
            margin-bottom: 5px;
        }
        
        .file-type {
            font-size: 14px;
            color: #666;
        }

        .preview-actions {
            margin-top: 20px;
        }

        .preview-actions .primary-btn {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            text-decoration: none;
            padding: 8px 16px;
        }

        .preview-actions .icon {
            font-size: 18px;
        }

        /* 修改文档标题相关样式 */
        .doc-title-cell {
            max-width: 300px;
            position: relative;
            overflow: visible; /* 修改这里，允许tooltip显示 */
        }

        .doc-title-wrapper {
            display: flex;
            align-items: flex-start;
            gap: 8px;
            overflow: visible; /* 修改这里，允许tooltip显示 */
        }

        .doc-title {
            flex: 1;
            min-width: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding-right: 60px;
            position: relative;
        }

        /* 修改tooltip样式 */
        .doc-title[title]:hover::after {
            content: attr(title);
            position: absolute;
            left: 0;
            top: 100%;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
            max-width: 300px;
            word-wrap: break-word;
            white-space: normal;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            pointer-events: none;
            margin-top: 5px;
        }

        .error-message {
            color: var(--error-color);
            margin: 10px 0;
            padding: 8px;
            background-color: rgba(255, 77, 79, 0.1);
            border-radius: 4px;
            font-size: 14px;
        }
        
        .preview-actions {
            margin-top: 15px;
        }

        /* 模板相关样式 */
        .templates-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .template-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            margin-bottom: 10px;
            background: #fafafa;
        }

        .template-item:hover {
            background: #f0f8ff;
        }

        .template-info {
            flex: 1;
        }

        .template-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .template-details {
            font-size: 12px;
            color: #666;
        }

        .template-actions {
            display: flex;
            gap: 8px;
        }

        .download-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .download-btn:hover {
            background: #218838;
        }

        .delete-template-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .delete-template-btn:hover {
            background: #c82333;
        }

        /* 模板管理表格列宽设置 */
        #templateManageModal .table-container th:nth-child(1),  /* 文件名列 */
        #templateManageModal .table-container td:nth-child(1) {
            width: 45%;
            min-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-weight: 500;
        }

        #templateManageModal .table-container th:nth-child(2),  /* 类型列 */
        #templateManageModal .table-container td:nth-child(2) {
            width: 20%;
            min-width: 100px;
            text-align: center;
        }

        #templateManageModal .table-container th:nth-child(3),  /* 上传时间列 */
        #templateManageModal .table-container td:nth-child(3) {
            width: 20%;
            min-width: 140px;
            text-align: center;
            font-size: 13px;
            color: #666;
        }

        #templateManageModal .table-container th:nth-child(4),  /* 操作列 */
        #templateManageModal .table-container td:nth-child(4) {
            width: 15%;
            min-width: 120px;
            text-align: center;
        }

        /* 类型列样式 - 简洁风格 */
        #templateManageModal .table-container td:nth-child(2) {
            font-weight: normal;
            color: #666;
        }

        /* 操作按钮 - 与网站整体风格一致 */
        #templateManageModal .download-btn,
        #templateManageModal .delete-template-btn {
            margin: 0 2px;
            padding: 5px 10px;
            font-size: 12px;
            border-radius: 3px;
            border: 1px solid;
            cursor: pointer;
            font-weight: normal;
        }

        #templateManageModal .download-btn {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }

        #templateManageModal .download-btn:hover {
            background: #218838;
            border-color: #218838;
        }

        #templateManageModal .delete-template-btn {
            background: #dc3545;
            color: white;
            border-color: #dc3545;
        }

        #templateManageModal .delete-template-btn:hover {
            background: #c82333;
            border-color: #c82333;
        }

        /* 移除文件图标，保持简洁 */

        /* 表头样式 - 与主表格保持一致 */
        #templateManageModal .table-container th {
            background: #f5f5f5;
            color: #333;
            font-weight: bold;
            border-bottom: 2px solid #ddd;
        }

        /* 表格行悬停效果 - 简洁风格 */
        #templateManageModal .table-container tbody tr:hover {
            background: #f9f9f9;
        }

        /* 确保模板管理表格使用固定布局 */
        #templateManageModal .table-container table {
            table-layout: fixed;
            width: 100%;
        }
</style>
</head>
<body>
    <div id="auth" class="container">
        <h1>欢迎使用公文取号系统</h1>
        <div class="form-group">
            <div id="loginForm">
                <div class="form-item">
                    <input id="loginUsername" placeholder="请输入用户名" autocomplete="username">
                    <div class="error"></div>
                </div>
                <div class="form-item">
                    <input id="loginPassword" type="password" placeholder="请输入密码" autocomplete="current-password">
                    <div class="error"></div>
                </div>
                <div class="btn-group">
                    <button onclick="handleLogin()" class="primary-btn">登录</button>
                    <button onclick="toggleForm('register')" class="secondary-btn">注册账号</button>
                </div>
            </div>

            <div id="registerForm" style="display: none;">
                <div class="form-item">
                    <input id="registerUsername" placeholder="请输入真实姓名" 
                           onkeyup="validateUsername(this.value)" autocomplete="username">
                    <div class="error"></div>
                </div>
                <div class="form-item">
                    <input id="registerPassword" type="password" 
                           placeholder="请输入密码（至少8位，包含字母和数字）" 
                           onkeyup="validatePassword(this.value)" autocomplete="new-password">
                    <div class="error"></div>
                    <div class="password-strength">
                        <div class="password-strength-bar"></div>
                    </div>
                    <div class="hint">密码强度：包含大小写字母、数字和特殊字符可提高安全性</div>
                </div>
                <div class="form-item">
                    <input id="confirmPassword" type="password" 
                           placeholder="请确认密码" 
                           onkeyup="validateConfirmPassword()" autocomplete="new-password">
                    <div class="error"></div>
                </div>
                <div class="btn-group">
                    <button onclick="handleRegister()" class="primary-btn">注册</button>
                    <button onclick="toggleForm('login')" class="secondary-btn">返回登录</button>
                </div>
            </div>
        </div>
    </div>

    <div id="main" class="container" style="display:none">
        <h1>公文取号系统</h1>
        <div class="user-info">
            <span id="userInfo"></span>
            <div>
                <button onclick="showChangePassword()" class="secondary-btn">修改密码</button>
                <button id="userManageBtn" onclick="showUserManage()" class="secondary-btn" style="display: none;">用户管理</button>
                <button id="templateManageBtn" onclick="showTemplateManage()" class="secondary-btn" style="display: none;">模板管理</button>
                <button onclick="handleLogout()" class="logout-btn">退出登录</button>
            </div>
        </div>
        
        <div class="form-group">
            <select id="docType">
                <option value="北张政发">北张政发</option>
                <option value="北张政呈">北张政呈</option>
                <option value="北张政函">北张政函</option>
            </select>
            <input id="docTitle" placeholder="请输入发文标题">
            <button onclick="handleCreateDoc()" class="primary-btn">获取公文号</button>
        </div>
        
        <div id="docResult" class="result-box" style="display:none"></div>
        
        <div class="form-group action-buttons">
            <button id="exportBtn" onclick="handleExport()" class="action-btn secondary-action">
                <span class="btn-icon">📊</span>
                <span class="btn-text">导出Excel</span>
            </button>
            <button id="downloadBtn" onclick="handleDownloadAll()" class="action-btn secondary-action">
                <span class="btn-icon">📁</span>
                <span class="btn-text">下载附件</span>
            </button>
            <button onclick="showTemplates()" class="action-btn secondary-action">
                <span class="btn-icon">📄</span>
                <span class="btn-text">文档模板</span>
            </button>
            <button onclick="showSearchPanel()" class="action-btn secondary-action">
                <span class="btn-icon">🔎</span>
                <span class="btn-text">高级查询</span>
            </button>
        </div>

        <!-- 查询面板 -->
        <div id="searchPanel" class="search-panel" style="display: none;">
            <div class="search-header">
                <h3>🔍 高级查询</h3>
                <button class="collapse-btn" onclick="toggleSearchPanel()">收起</button>
            </div>
            <div class="search-form">
                <div class="search-row">
                    <div class="search-item">
                        <label>文档类型：</label>
                        <select id="searchType">
                            <option value="">全部类型</option>
                            <option value="北张政发">北张政发</option>
                            <option value="北张政呈">北张政呈</option>
                            <option value="北张政函">北张政函</option>
                        </select>
                    </div>
                    <div class="search-item">
                        <label>年份：</label>
                        <select id="searchYear">
                            <option value="">全部年份</option>
                        </select>
                    </div>
                    <div class="search-item">
                        <label>创建者：</label>
                        <select id="searchCreator">
                            <option value="">全部用户</option>
                        </select>
                    </div>
                </div>
                <div class="search-row">
                    <div class="search-item">
                        <label>开始时间：</label>
                        <input type="date" id="searchStartDate">
                    </div>
                    <div class="search-item">
                        <label>结束时间：</label>
                        <input type="date" id="searchEndDate">
                    </div>
                    <div class="search-item">
                        <label>文档号：</label>
                        <input type="text" id="searchNumber" placeholder="如：1、2-5、10">
                    </div>
                </div>
                <div class="search-row">
                    <div class="search-item search-item-full">
                        <label>标题关键词：</label>
                        <input type="text" id="searchKeyword" placeholder="输入标题关键词进行搜索">
                    </div>
                </div>
                <div class="search-actions">
                    <button onclick="performSearch()" class="primary-btn">🔍 查询</button>
                    <button onclick="clearSearch()" class="secondary-btn">🗑️ 清空</button>
                    <button onclick="resetToAll()" class="secondary-btn">📋 显示全部</button>
                </div>
            </div>
        </div>

        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>类型</th>
                        <th>公文号</th>
                        <th>标题</th>
                        <th>创建者</th>
                        <th>附件</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="docList"></tbody>
            </table>
        </div>
        
        <div class="pagination">
            <button onclick="handlePageChange(-1)" id="prevPage">上一页</button>
            <span id="pageInfo"></span>
            <button onclick="handlePageChange(1)" id="nextPage">下一页</button>
        </div>
    </div>

    <div id="changePasswordModal" class="modal" style="display: none;">
        <div class="modal-content">
            <h2>修改密码</h2>
            <div class="form-group">
                <div id="changePasswordError" class="error-message" style="color: red; margin-bottom: 10px;"></div>
                <div class="form-item">
                    <input type="password" id="oldPassword" placeholder="请输入原密码" onkeyup="validateChangePassword()">
                    <div class="error"></div>
                </div>
                <div class="form-item">
                    <input type="password" id="newPassword" placeholder="请输入新密码（至少8位，包含字母和数字）" onkeyup="validateChangePassword()">
                    <div class="error"></div>
                </div>
                <div class="form-item">
                    <input type="password" id="confirmNewPassword" placeholder="请确认新密码" onkeyup="validateChangePassword()">
                    <div class="error"></div>
                </div>
            </div>
            <div class="btn-group">
                <button onclick="doChangePassword()" class="primary-btn">确认修改</button>
                <button onclick="hideChangePassword()" class="secondary-btn">取消</button>
            </div>
        </div>
    </div>

    <!-- 添加修改标题的模态框 -->
    <div id="editTitleModal" class="modal" style="display: none;">
        <div class="modal-content">
            <h2>修改标题</h2>
            <div class="form-group">
                <div class="form-item">
                    <input type="text" id="newTitle" placeholder="请输入新标题">
                    <div class="error"></div>
                </div>
            </div>
            <div class="btn-group">
                <button onclick="handleUpdateTitle()" class="primary-btn">确认修改</button>
                <button onclick="hideEditTitleModal()" class="secondary-btn">取消</button>
            </div>
        </div>
    </div>

    <!-- 添加预览模态框 -->
    <div id="previewModal" class="preview-modal" style="display: none;">
        <div class="preview-content">
            <span class="preview-close" onclick="hidePreview()">×</span>
            <div id="previewContainer"></div>
        </div>
    </div>

    <!-- 用户管理模态框 -->
    <div id="userManageModal" class="modal" style="display: none;">
        <div class="modal-content user-manage-modal">
            <div class="modal-header">
                <h2>👥 用户管理</h2>
                <button class="close-btn" onclick="hideUserManage()">×</button>
            </div>

            <div class="user-stats">
                <div class="stat-card">
                    <div class="stat-number" id="totalUsers">0</div>
                    <div class="stat-label">总用户数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="adminUsers">0</div>
                    <div class="stat-label">管理员</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="normalUsers">0</div>
                    <div class="stat-label">普通用户</div>
                </div>
            </div>

            <div class="user-search">
                <input type="text" id="userSearchInput" placeholder="🔍 搜索用户名..." onkeyup="filterUsers()">
            </div>

            <div class="user-table-container">
                <table class="user-table">
                    <thead>
                        <tr>
                            <th>👤 用户信息</th>
                            <th>🏷️ 角色</th>
                            <th>📅 注册时间</th>
                            <th>📊 状态</th>
                            <th>⚙️ 操作</th>
                        </tr>
                    </thead>
                    <tbody id="userList"></tbody>
                </table>
            </div>

            <div class="modal-footer">
                <button onclick="refreshUserList()" class="refresh-btn">🔄 刷新</button>
                <button onclick="hideUserManage()" class="secondary-btn">关闭</button>
            </div>
        </div>
    </div>

    <!-- 注销用户模态框 -->
    <div id="deactivateUserModal" class="modal" style="display: none;">
        <div class="modal-content deactivate-modal">
            <div class="modal-header">
                <h2>🗑️ 注销用户账号</h2>
                <button class="close-btn" onclick="hideDeactivateUser()">×</button>
            </div>

            <div class="warning-section">
                <div class="warning-icon">⚠️</div>
                <div class="warning-content">
                    <h3>危险操作警告</h3>
                    <p>此操作将<strong>永久删除</strong>用户账号，无法恢复！</p>
                </div>
            </div>

            <div class="user-info-section">
                <div class="info-item">
                    <span class="info-label">👤 用户名：</span>
                    <span id="deactivateUsername" class="info-value"></span>
                </div>
                <div class="info-item">
                    <span class="info-label">📋 后果：</span>
                    <span class="info-value">该用户需要重新注册才能使用系统</span>
                </div>
            </div>

            <div id="deactivateUserError" class="error-message"></div>

            <div class="modal-footer">
                <button onclick="doDeactivateUser()" class="danger-btn">
                    🗑️ 确认注销
                </button>
                <button onclick="hideDeactivateUser()" class="secondary-btn">取消</button>
            </div>
        </div>
    </div>

    <!-- 模板列表模态框 -->
    <div id="templatesModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 800px;">
            <h2>📄 文档模板</h2>
            <div class="form-group">
                <p style="color: #666; margin-bottom: 15px;">下载标准文档模板，确保格式规范统一</p>
                <div id="templatesList" class="templates-list">
                    <!-- 模板列表将在这里动态加载 -->
                </div>
            </div>
            <div class="btn-group">
                <button onclick="hideTemplates()" class="secondary-btn">关闭</button>
            </div>
        </div>
    </div>

    <!-- 模板管理模态框（仅管理员） -->
    <div id="templateManageModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 900px;">
            <h2>📄 模板管理</h2>
            <div class="form-group">
                <!-- 上传新模板 -->
                <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                    <h3 style="margin: 0 0 15px 0;">上传新模板</h3>
                    <div id="templateUploadError" class="error-message" style="color: red; margin-bottom: 10px;"></div>
                    <div class="form-item">
                        <input type="text" id="templateName" placeholder="模板名称（必填）">
                    </div>
                    <div class="form-item">
                        <input type="text" id="templateDescription" placeholder="模板描述（选填）">
                    </div>
                    <div class="form-item">
                        <input type="file" id="templateFile" accept=".doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.txt">
                    </div>
                    <button onclick="uploadTemplate()" class="primary-btn">上传模板</button>
                </div>
                
                <!-- 模板列表 -->
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>文件名</th>
                                <th>类型</th>
                                <th>上传时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="templatesManageList"></tbody>
                    </table>
                </div>
            </div>
            <div class="btn-group">
                <button onclick="hideTemplateManage()" class="secondary-btn">关闭</button>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script>
        // 动态获取API地址
        const getApiUrl = () => {
            const ports = [3000, 3001, 3002, 3003];  // 可能的端口列表
            const serverHost = window.location.hostname;  // 使用当前页面的主机名
            
            return new Promise((resolve, reject) => {
                let portIndex = 0;
                
                function tryPort() {
                    if (portIndex >= ports.length) {
                        reject(new Error('无法连接到服务器'));
                        return;
                    }
                    
                    const port = ports[portIndex];
                    const url = `http://${serverHost}:${port}/api`;
                    
                    fetch(url + '/ping')
                        .then(response => {
                            if (response.ok) {
                                resolve(url);
                            } else {
                                portIndex++;
                                tryPort();
                            }
                        })
                        .catch(() => {
                            portIndex++;
                            tryPort();
                        });
                }
                
                tryPort();
            });
        };

        // 初始化API_URL
        let API_URL = `http://${window.location.hostname}:3001/api`;  // 默认值使用当前主机名
        
        // 在页面加载时尝试连接服务器
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                API_URL = await getApiUrl();
                const user = localStorage.getItem('user');
                user && showMain();
            } catch (error) {
                showToast('无法连接到服务器，请检查服务器是否启动');
            }
        });

        const $ = id => document.getElementById(id);
        let currentPage = 1;
        let totalPages = 1;
        let currentForm = 'login';
        let isRegistering = false;
        let currentSearchParams = null; // 当前查询参数
        let isSearchMode = false; // 是否处于查询模式
        const formValidation = {
            username: false,
            password: false,
            confirmPassword: false
        };

        const showToast = message => {
            const existingToast = document.querySelector('.toast');
            if (existingToast) {
                existingToast.remove();
            }

            const toast = document.createElement('div');
            toast.className = 'toast';
            toast.textContent = message;
            document.body.appendChild(toast);
            
            toast.style.zIndex = '10000';
            
            requestAnimationFrame(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translate(-50%, 0)';
            });

            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translate(-50%, -20px)';
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        };

        const api = {
            async request(endpoint, options = {}) {
                try {
                    // 构建基础请求选项
                    const requestOptions = {
                        ...options,
                        headers: {
                            'Accept': 'application/json'
                        }
                    };

                    // 如果不是登录或注册请求，需要验证用户信息
                    if (!endpoint.startsWith('/login') && !endpoint.startsWith('/register')) {
                        const userStr = localStorage.getItem('user');
                        if (!userStr) {
                            throw new Error('未找到用户信息，请重新登录');
                        }

                        const user = JSON.parse(userStr);
                        if (!user || !user.id) {
                            throw new Error('用户信息不完整，请重新登录');
                        }

                        // 添加用户ID到请求头
                        requestOptions.headers['user-id'] = user.id.toString();
                    }

                    // 如果不是 FormData，添加 Content-Type
                    if (!(options.body instanceof FormData)) {
                        requestOptions.headers['Content-Type'] = 'application/json';
                    }

                    // 合并自定义headers
                    if (options.headers) {
                        requestOptions.headers = {
                            ...requestOptions.headers,
                            ...options.headers
                        };
                    }

                    const response = await fetch(API_URL + endpoint, requestOptions);
                    
                    if (!response.ok) {
                        const data = await response.json().catch(() => ({}));
                        if (data.error === '未找到用户信息，请重新登录' && !endpoint.startsWith('/login') && !endpoint.startsWith('/register')) {
                            localStorage.removeItem('user');
                            handleLogout();
                        }
                        throw new Error(data.error || '操作失败');
                    }
                    
                    return await response.json();
                } catch (error) {
                    if (error.message.includes('未找到用户信息') || error.message.includes('用户信息不完整')) {
                        if (!endpoint.startsWith('/login') && !endpoint.startsWith('/register')) {
                            handleLogout();
                        }
                    }
                    throw error;
                }
            },

            auth: (type, credentials) => api.request('/' + type, {
                method: 'POST',
                body: JSON.stringify(credentials)
            }),

            createDoc: (data) => api.request('/documents', {
                method: 'POST',
                body: JSON.stringify(data)
            }),

            getDocs: (page) => api.request(`/documents?page=${page}&t=${Date.now()}`),

            uploadAttachments: (docId, files) => {
                const formData = new FormData();
                Array.from(files).forEach(file => {
                    // 使用原始文件名
                    formData.append('files', file);
                });

                return api.request(`/documents/${docId}/attachments`, {
                    method: 'POST',
                    body: formData
                });
            },

            deleteDoc: (docId) => api.request(`/documents/${docId}`, {
                method: 'DELETE'
            }),

            changePassword: (data) => api.request('/change-password', {
                method: 'POST',
                body: JSON.stringify(data)
            }),

            updateTitle: (docId, title) => api.request(`/documents/${docId}/title`, {
                method: 'PUT',
                body: JSON.stringify({ title })
            }),

            deleteAttachment: (attachmentId) => api.request(`/attachments/${attachmentId}`, {
                method: 'DELETE'
            }),

            getUsers: () => api.request('/users'),

            deactivateUser: (data) => api.request('/deactivate-user', {
                method: 'POST',
                body: JSON.stringify(data)
            }),

            // 查询相关API
            searchDocs: (params) => {
                const queryString = new URLSearchParams(params).toString();
                return api.request(`/documents/search?${queryString}&t=${Date.now()}`);
            },

            getSearchOptions: () => api.request('/documents/search-options'),

            // 模板相关API
            getTemplates: () => api.request('/templates'),

            uploadTemplate: (formData) => {
                return api.request('/templates', {
                    method: 'POST',
                    body: formData
                });
            },

            downloadTemplate: (templateId) => {
                const user = JSON.parse(localStorage.getItem('user'));
                return fetch(`${API_URL}/templates/${templateId}/download`, {
                    headers: {
                        'user-id': user.id
                    }
                });
            },

            deleteTemplate: (templateId) => api.request(`/templates/${templateId}`, {
                method: 'DELETE'
            })
        };

        function toggleForm(form) {
            currentForm = form;
            $('loginForm').style.display = form === 'login' ? 'block' : 'none';
            $('registerForm').style.display = form === 'register' ? 'block' : 'none';
            
            // 清空表单和错误提示
            ['loginUsername', 'loginPassword', 'registerUsername', 'registerPassword', 'confirmPassword'].forEach(id => {
                const input = $(id);
                if (input) {
                    input.value = '';
                    const errorDiv = input.nextElementSibling;
                    if (errorDiv && errorDiv.className === 'error') {
                        errorDiv.textContent = '';
                    }
                }
            });
            
            // 重置密码强度条
            const strengthBar = document.querySelector('.password-strength-bar');
            if (strengthBar) {
                strengthBar.className = 'password-strength-bar';
            }
            
            // 重置表单验证状态
            Object.keys(formValidation).forEach(key => formValidation[key] = false);
        }

        function validateUsername(username) {
            const input = $('registerUsername');
            const errorDiv = input.nextElementSibling;
            
            if (!username) {
                errorDiv.textContent = '请输入用户名';
                formValidation.username = false;
                return;
            }
            
            if (username.length < 2 || username.length > 16) {
                errorDiv.textContent = '用户名长度应为2-16位';
                formValidation.username = false;
                return;
            }
            
            errorDiv.textContent = '';
            formValidation.username = true;
        }

        function validatePassword(password) {
            const input = $('registerPassword');
            const errorDiv = input.nextElementSibling;
            const strengthBar = document.querySelector('.password-strength-bar');
            
            if (!password) {
                errorDiv.textContent = '请输入密码';
                strengthBar.className = 'password-strength-bar';
                formValidation.password = false;
                return;
            }
            
            if (password.length < 8) {
                errorDiv.textContent = '密码长度至少8位';
                strengthBar.className = 'password-strength-bar';
                formValidation.password = false;
                return;
            }
            
            if (!/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/.test(password)) {
                errorDiv.textContent = '密码必须包含字母和数字';
                formValidation.password = false;
            } else {
                errorDiv.textContent = '';
                formValidation.password = true;
            }
            
            // 计算密码强度
            let strength = 0;
            if (password.length >= 8) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/\d/.test(password)) strength++;
            if (/[@$!%*#?&]/.test(password)) strength++;
            
            // 更新密码强度条
            strengthBar.className = 'password-strength-bar ' + 
                (strength <= 2 ? 'strength-weak' : 
                 strength <= 3 ? 'strength-medium' : 
                 'strength-strong');
            
            // 验证确认密码
            validateConfirmPassword();
        }

        function validateConfirmPassword() {
            const password = $('registerPassword').value.trim();
            const confirmPassword = $('confirmPassword').value.trim();
            const errorDiv = $('confirmPassword').nextElementSibling;
            
            if (!confirmPassword) {
                errorDiv.textContent = '请确认密码';
                formValidation.confirmPassword = false;
                return;
            }
            
            if (confirmPassword !== password) {
                errorDiv.textContent = '两次输入的密码不一致';
                formValidation.confirmPassword = false;
                return;
            }
            
            errorDiv.textContent = '';
            formValidation.confirmPassword = true;
        }

        async function handleLogin() {
            const username = $('loginUsername').value.trim();
            const password = $('loginPassword').value.trim();
            
            // 清除之前的错误提示
            $('loginUsername').nextElementSibling.textContent = '';
            $('loginPassword').nextElementSibling.textContent = '';
            
            if (!username) {
                $('loginUsername').nextElementSibling.textContent = '请输入用户名';
                return;
            }
            
            if (!password) {
                $('loginPassword').nextElementSibling.textContent = '请输入密码';
                return;
            }
            
            try {
                const response = await fetch(API_URL + '/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || '登录失败');
                }

                if (data.id) {
                    // 确保存储完整的用户信息
                    const userInfo = {
                        id: data.id,
                        username: data.username,
                        roles: data.roles || [],
                        permissions: data.permissions || []
                    };
                    localStorage.setItem('user', JSON.stringify(userInfo));
                    showMain();
                    showToast('登录成功');
                } else {
                    throw new Error('登录失败：用户信息不完整');
                }
            } catch (error) {
                $('loginPassword').nextElementSibling.textContent = error.message;
            }
        }

        async function handleRegister() {
            if (!Object.values(formValidation).every(valid => valid)) {
                showToast('请检查输入是否正确');
                return;
            }
            
            const username = $('registerUsername').value.trim();
            const password = $('registerPassword').value.trim();
            
            try {
                await api.auth('register', { username, password });
                showToast('注册成功，请登录');
                toggleForm('login');
            } catch (error) {
                showToast(error.message || '注册失败，请重试');
            }
        }

        function handleLogout() {
            localStorage.removeItem('user');
            $('auth').style.display = 'block';
            $('main').style.display = 'none';
            
            // 清除所有表单和结果
            $('docResult').style.display = 'none';
            $('docResult').innerHTML = '';
            $('docTitle').value = '';
            $('docType').selectedIndex = 0;
            
            // 清空登录表单
            const loginUsername = $('loginUsername');
            const loginPassword = $('loginPassword');
            
            if (loginUsername) {
                loginUsername.value = '';
                if (loginUsername.nextElementSibling) {
                    loginUsername.nextElementSibling.textContent = '';
                }
            }
            
            if (loginPassword) {
                loginPassword.value = '';
                if (loginPassword.nextElementSibling) {
                    loginPassword.nextElementSibling.textContent = '';
                }
            }
            
            // 重置页面状态
            currentPage = 1;
            totalPages = 1;
            
            // 切换到登录表单
            if (currentForm !== 'login') {
                toggleForm('login');
            }
        }

        async function handleCreateDoc() {
            const title = $('docTitle').value.trim();
            if (!title) return showToast('请输入发文标题');

            try {
                const user = JSON.parse(localStorage.getItem('user'));
                if (!user || !user.id) {
                    showToast('登录信息已失效，请重新登录');
                    handleLogout();
                    return;
                }

                const doc = await api.createDoc({
                    type: $('docType').value,
                    title
                });
                $('docResult').innerHTML = `
                    <div class="number-result">${doc.number}</div>
                    <div style="margin-top: 10px; color: #666;">
                        <span style="color: var(--primary-color);">✨ 提示：</span>
                        您可以在下方列表中为该公文上传附件
                    </div>
                `;
                $('docResult').style.display = 'block';
                $('docTitle').value = '';
                
                // 添加自动滚动到新创建的文档
                await loadDocs();
                setTimeout(() => {
                    const newDoc = document.querySelector(`[data-doc-id="${doc.id}"]`);
                    if (newDoc) {
                        newDoc.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        // 添加高亮效果
                        newDoc.style.backgroundColor = '#e6f7ff';
                        setTimeout(() => {
                            newDoc.style.transition = 'background-color 0.5s ease';
                            newDoc.style.backgroundColor = '';
                        }, 2000);
                    }
                }, 100);
            } catch (error) {
                console.error(error);
                showToast(error.message || '创建失败');
            }
        }

        let currentEditingDocId = null;

        async function loadDocs() {
            try {
                let data;

                if (isSearchMode && currentSearchParams) {
                    // 查询模式
                    const result = await api.searchDocs({ ...currentSearchParams, page: currentPage });
                    data = {
                        records: result.documents,
                        totalPages: result.pagination.totalPages
                    };
                    showSearchResultInfo(result.total, currentSearchParams);
                } else {
                    // 正常模式
                    data = await api.getDocs(currentPage);
                    hideSearchResultInfo();
                }

                totalPages = data.totalPages;

                $('docList').innerHTML = data.records.map(doc => `
                    <tr data-doc-id="${doc.id}">
                        <td>${new Date(doc.created_at).toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' })}</td>
                        <td>${doc.type}</td>
                        <td>${doc.formatted_number}</td>
                        <td class="doc-title-cell">
                            <div class="doc-title-wrapper">
                                <div class="doc-title" title="${doc.title}">
                                    ${doc.title}
                                </div>
                                <div class="action-buttons">
                                    <button class="icon-btn" onclick="showEditTitleModal(${doc.id}, '${doc.title}')" title="修改标题">
                                        <span class="icon">✎</span>
                                    </button>
                                </div>
                            </div>
                        </td>
                        <td>${doc.creator_name || '未知'}</td>
                        <td class="upload-area">
                                <div class="attachments-preview">
                                ${doc.attachments && doc.attachments.length > 0 ? 
                                    doc.attachments.map(attachment => `
                                        <div class="attachment-item" onclick="handlePreviewAttachment(${attachment.id})">
                                            <div class="attachment-preview">
                                                ${attachment.file_type === 'image' 
                                                    ? `<img src="${attachment.filepath}" alt="附件预览">`
                                                    : getFileIcon(attachment.file_type)
                                                }
                                </div>
                                            <div class="attachment-info">
                                                <div class="attachment-name" title="${attachment.filename}">
                                                    ${attachment.filename}
                                                </div>
                                                <div class="attachment-type">
                                                    ${getFileTypeName(attachment.file_type)}
                                                    <span class="attachment-size">${formatFileSize(attachment.size || 0)}</span>
                                                </div>
                                            </div>
                                            <div class="attachment-delete" onclick="handleDeleteAttachment(event, ${attachment.id})">×</div>
                                        </div>
                                    `).join('') 
                                    : ''
                                }
                                <label class="upload-label" for="file${doc.id}">
                                    <span class="icon">➕</span> 上传附件
                                </label>
                            </div>
                            <input type="file" id="file${doc.id}" multiple 
                                onchange="handleUpload(${doc.id}, this.files)" 
                                style="display:none">
                        </td>
                        <td>
                            <button class="secondary-btn" onclick="handleDelete(${doc.id})">删除</button>
                        </td>
                    </tr>
                `).join('');

                $('pageInfo').textContent = `第 ${currentPage} 页 / 共 ${totalPages} 页`;
                $('prevPage').disabled = currentPage < 2;
                $('nextPage').disabled = currentPage >= totalPages;
            } catch (error) {
                showToast('加载失败，请刷新页面重试');
            }
        }

        async function handleUpload(docId, files) {
            if (!files?.length) return;

            // 添加文件大小限制检查（50MB）
            const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
            const oversizedFiles = Array.from(files).filter(file => file.size > MAX_FILE_SIZE);
            
            if (oversizedFiles.length > 0) {
                const fileNames = oversizedFiles.map(f => f.name).join(', ');
                showToast(`以下文件超过50MB限制：${fileNames}`);
                return;
            }

            try {
                const formData = new FormData();
                Array.from(files).forEach(file => {
                    formData.append('files', file);
                });

                const response = await fetch(`${API_URL}/documents/${docId}/attachments`, {
                    method: 'POST',
                    headers: {
                        'user-id': JSON.parse(localStorage.getItem('user')).id
                    },
                    body: formData
                });

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.error || '上传失败');
                }

                const data = await response.json();

                if (!data.attachments || !Array.isArray(data.attachments)) {
                    throw new Error('上传响应格式错误');
                }

                showToast('上传成功');
                await loadDocs();
            } catch (error) {
                showToast(error.message || '上传失败，请重试');
            }
        }

        async function handleDelete(docId) {
            if (!confirm('确定要删除这条记录吗？')) return;
            try {
                await api.deleteDoc(docId);
                showToast('删除成功');
                loadDocs();
            } catch (error) {
                showToast(error.message || '删除失败，请检查权限');
                console.error(error);
            }
        }

        function handlePageChange(delta) {
            const newPage = currentPage + delta;
            if (newPage > 0 && newPage <= totalPages) {
                currentPage = newPage;

                if (isSearchMode && currentSearchParams) {
                    // 搜索模式下的分页
                    handleSearch();
                } else {
                    // 正常模式下的分页
                    loadDocs();
                }
            }
        }

        async function handleExport() {
            try {
                // 获取第一页数据以获取总页数
                const firstPageData = await api.getDocs(1);
                const totalPages = firstPageData.totalPages;
                
                if (!firstPageData?.records?.length) {
                    return showToast('暂无数据可导出');
                }

                // 创建一个数组来存储所有文档
                let allDocs = [...firstPageData.records];

                // 获取剩余页面的数据
                for (let page = 2; page <= totalPages; page++) {
                    const pageData = await api.getDocs(page);
                    if (pageData?.records) {
                        allDocs = allDocs.concat(pageData.records);
                    }
                }

                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.aoa_to_sheet([
                    ['序号', '时间', '类型', '公文号', '标题', '创建者', '附件数量'],
                    ...allDocs.map((r, i) => [
                        i + 1,
                        new Date(r.created_at).toLocaleString(),
                        r.type,
                        r.formatted_number,
                        r.title,
                        r.creator_name || '未知',
                        r.attachments?.length || 0
                    ])
                ]);

                // 设置列宽
                const colWidths = [
                    { wch: 6 },  // 序号
                    { wch: 20 }, // 时间
                    { wch: 12 }, // 类型
                    { wch: 15 }, // 公文号
                    { wch: 40 }, // 标题
                    { wch: 12 }, // 创建者
                    { wch: 8 }   // 附件数量
                ];
                ws['!cols'] = colWidths;

                XLSX.utils.book_append_sheet(wb, ws, '公文列表');
                XLSX.writeFile(wb, '公文号.xlsx');
                showToast('导出成功');
            } catch (error) {
                showToast('导出失败，请重试');
                console.error('导出失败:', error);
            }
        }

        async function handleDownloadAll() {
            try {
                // 获取第一页数据以获取总页数
                const firstPageData = await api.getDocs(1);
                const totalPages = firstPageData.totalPages;
                
                if (!firstPageData?.records?.length) {
                    return showToast('暂无附件可下载');
                }

                // 创建一个数组来存储所有文档
                let allDocs = [...firstPageData.records];

                // 获取剩余页面的数据
                for (let page = 2; page <= totalPages; page++) {
                    const pageData = await api.getDocs(page);
                    if (pageData?.records) {
                        allDocs = allDocs.concat(pageData.records);
                    }
                }

                // 过滤出有附件的文档
                const docsWithAttachments = allDocs.filter(r => r.attachments?.length);
                
                if (!docsWithAttachments.length) {
                    return showToast('暂无附件可下载');
                }

                const zip = new JSZip();
                for (const doc of docsWithAttachments) {
                    const folder = zip.folder(doc.formatted_number);
                    for (const attachment of doc.attachments) {
                        try {
                            const response = await fetch(`${API_URL}/attachments/${attachment.id}`, {
                                headers: {
                                    'user-id': JSON.parse(localStorage.getItem('user')).id
                                }
                            });
                            if (!response.ok) throw new Error(`下载失败: ${attachment.filename}`);
                            const blob = await response.blob();
                            folder.file(attachment.filename, blob);
                        } catch (error) {
                            showToast(`下载 ${attachment.filename} 失败`);
                        }
                    }
                }

                const blob = await zip.generateAsync({ type: 'blob' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = '附件.zip';
                a.click();
                URL.revokeObjectURL(url);
                showToast('下载成功');
            } catch (error) {
                showToast('下载失败，请重试');
            }
        }

        function showMain() {
            const user = JSON.parse(localStorage.getItem('user'));
            if (user) {
                const isAdmin = user.roles?.some(r => r.name === 'admin');
                const hasExportPermission = user.permissions?.includes('export_excel');
                const hasDownloadPermission = user.permissions?.includes('download_attachments');

                $('userInfo').textContent = `当前用户：${user.username}`;

                // 清除之前的公文号结果
                $('docResult').style.display = 'none';
                $('docResult').innerHTML = '';
                $('docTitle').value = '';
                
                // 根据权限显示/隐藏功能按钮
                const exportBtn = $('exportBtn');
                const downloadBtn = $('downloadBtn');
                const userManageBtn = $('userManageBtn');
                const templateManageBtn = $('templateManageBtn');

                if (exportBtn) {
                    exportBtn.style.display = hasExportPermission ? 'flex' : 'none';
                }

                if (downloadBtn) {
                    downloadBtn.style.display = hasDownloadPermission ? 'flex' : 'none';
                }

                // 如果是管理员，显示管理按钮
                if (userManageBtn) {
                    userManageBtn.style.display = isAdmin ? 'inline-block' : 'none';
                }
                
                if (templateManageBtn) {
                    templateManageBtn.style.display = isAdmin ? 'inline-block' : 'none';
                }

                $('auth').style.display = 'none';
                $('main').style.display = 'block';
                loadDocs();
            }
        }

        function showChangePassword() {
            $('changePasswordModal').style.display = 'flex';
            setTimeout(bindEvents, 100);
        }

        function hideChangePassword() {
            $('changePasswordModal').style.display = 'none';
            ['oldPassword', 'newPassword', 'confirmNewPassword'].forEach(id => {
                const input = $(id);
                if (input) {
                    input.value = '';
                    const errorDiv = input.nextElementSibling;
                    if (errorDiv && errorDiv.className === 'error') {
                        errorDiv.textContent = '';
                    }
                }
            });
        }

        function validateChangePassword() {
            const oldPassword = $('oldPassword');
            const newPassword = $('newPassword');
            const confirmNewPassword = $('confirmNewPassword');
            let isValid = true;
            
            [oldPassword, newPassword, confirmNewPassword].forEach(input => {
                const errorDiv = input.nextElementSibling;
                if (errorDiv) {
                    errorDiv.textContent = '';
                }
            });
            
            if (!oldPassword.value.trim()) {
                oldPassword.nextElementSibling.textContent = '请输入原密码';
                isValid = false;
            }
            
            const newPwd = newPassword.value.trim();
            if (!newPwd) {
                newPassword.nextElementSibling.textContent = '请输入新密码';
                isValid = false;
            } else if (newPwd.length < 8) {
                newPassword.nextElementSibling.textContent = '新密码长度不能少于8位';
                isValid = false;
            } else if (!/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/.test(newPwd)) {
                newPassword.nextElementSibling.textContent = '新密码必须包含字母和数字';
                isValid = false;
            } else if (newPwd === oldPassword.value.trim()) {
                newPassword.nextElementSibling.textContent = '新密码不能与原密码相同';
                isValid = false;
            }
            
            const confirmPwd = confirmNewPassword.value.trim();
            if (!confirmPwd) {
                confirmNewPassword.nextElementSibling.textContent = '请确认新密码';
                isValid = false;
            } else if (confirmPwd !== newPwd) {
                confirmNewPassword.nextElementSibling.textContent = '两次输入的新密码不一致';
                isValid = false;
            }
            
            return isValid;
        }

        async function doChangePassword() {
            if (!validateChangePassword()) {
                return;
            }

            const submitBtn = document.querySelector('#changePasswordModal .primary-btn');
            submitBtn.disabled = true;
            submitBtn.textContent = '修改中...';

            try {
                const user = JSON.parse(localStorage.getItem('user'));
                if (!user || !user.id) {
                    showToast('登录信息已失效，请重新登录');
                    hideChangePassword();
                    handleLogout();
                    return;
                }

                const oldPassword = $('oldPassword').value.trim();
                const newPassword = $('newPassword').value.trim();

                const response = await fetch(API_URL + '/change-password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'user-id': user.id
                    },
                    body: JSON.stringify({
                        userId: user.id,
                        oldPassword: oldPassword,
                        newPassword: newPassword
                    })
                });

                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || '修改密码失败');
                }

                hideChangePassword();
                showToast('密码修改成功！');
            } catch (error) {
                $('changePasswordError').textContent = error.message;
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '确认修改';
            }
        }

        function bindEvents() {
            const submitBtn = document.getElementById('submitChangePassword');
            if (submitBtn) {
                submitBtn.onclick = async function(e) {
                    e.preventDefault();
                    await handleChangePassword();
                };
            }
        }

        function showEditTitleModal(docId, currentTitle) {
            currentEditingDocId = docId;
            $('newTitle').value = currentTitle;
            $('editTitleModal').style.display = 'flex';
        }

        function hideEditTitleModal() {
            currentEditingDocId = null;
            $('editTitleModal').style.display = 'none';
            $('newTitle').value = '';
        }

        async function handleUpdateTitle() {
            const newTitle = $('newTitle').value.trim();
            if (!newTitle) {
                showToast('请输入新标题');
                return;
            }

            try {
                await api.updateTitle(currentEditingDocId, newTitle);
                showToast('标题修改成功');
                hideEditTitleModal();
                loadDocs();
            } catch (error) {
                showToast(error.message || '修改失败');
            }
        }

        function getFileIcon(fileType) {
            const icons = {
                'pdf': '<span class="file-icon file-icon-pdf">📄</span>',
                'office': '<span class="file-icon file-icon-office">📝</span>',
                'image': '<span class="file-icon file-icon-image">🖼️</span>',
                'other': '<span class="file-icon file-icon-other">📎</span>'
            };
            return icons[fileType] || icons.other;
        }

        function formatFileSize(size) {
            if (size < 1024) return size + ' B';
            if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB';
            return (size / (1024 * 1024)).toFixed(1) + ' MB';
        }

        function getFileTypeName(fileType) {
            const typeNames = {
                'pdf': 'PDF 文档',
                'office': 'Office 文档',
                'image': '图片',
                'other': '文件'
            };
            return typeNames[fileType] || '文件';
        }

        function showPreview(attachment) {
            const modal = $('previewModal');
            const container = $('previewContainer');
            
            // 清空容器
            container.innerHTML = '';
            
            // 解码文件名（如果需要）
            const filename = decodeURIComponent(attachment.filename);
            
            // 获取用户权限
            const user = JSON.parse(localStorage.getItem('user'));
            const hasDownloadPermission = user.permissions?.includes('download_attachments');
            
            switch (attachment.file_type) {
                case 'image':
                    const img = document.createElement('img');
                    const userId = JSON.parse(localStorage.getItem('user')).id;
                    img.src = `${API_URL}/attachments/${attachment.id}/preview?userId=${userId}`;  // 使用专门的预览接口
                    img.className = 'preview-image';
                    container.appendChild(img);
                    break;
                    
                case 'pdf':
                        container.innerHTML = `
                            <div style="text-align: center; padding: 20px;">
                                <div style="font-size: 48px;">${getFileIcon(attachment.file_type)}</div>
                                <div class="file-info">
                                    <div class="file-name">${filename}</div>
                                    <div class="file-type">${getFileTypeName(attachment.file_type)}</div>
                                </div>
                                <div class="preview-actions">
                                ${hasDownloadPermission ? 
                                    `<button onclick="handleDownloadAttachment(${attachment.id}, '${filename}')" class="primary-btn">
                                        <span class="icon">📥</span> 下载文件
                                    </button>` :
                                    '<p class="error-message">您没有下载权限</p>'
                                }
                                </div>
                            </div>
                        `;
                    break;
                    
                case 'office':
                default:
                    container.innerHTML = `
                        <div style="text-align: center; padding: 20px;">
                            <div style="font-size: 48px;">${getFileIcon(attachment.file_type)}</div>
                            <div class="file-info">
                                <div class="file-name">${filename}</div>
                                <div class="file-type">${getFileTypeName(attachment.file_type)}</div>
                            </div>
                            <div class="preview-actions">
                                ${hasDownloadPermission ? 
                                    `<button onclick="handleDownloadAttachment(${attachment.id}, '${filename}')" class="primary-btn">
                                        <span class="icon">📥</span> 下载文件
                                    </button>` :
                                    '<p class="error-message">您没有下载权限</p>'
                                }
                            </div>
                        </div>
                    `;
            }
            
            modal.style.display = 'flex';
        }

        // 添加下载附件的处理函数
        async function handleDownloadAttachment(attachmentId, filename) {
            try {
                const response = await fetch(`${API_URL}/attachments/${attachmentId}`, {
                    headers: {
                        'user-id': JSON.parse(localStorage.getItem('user')).id
                    }
                });
                
                if (!response.ok) {
                    throw new Error('下载失败');
                }
                
                const blob = await response.blob();
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                a.click();
                URL.revokeObjectURL(url);
            } catch (error) {
                showToast(error.message || '下载失败');
            }
        }

        function hidePreview() {
            $('previewModal').style.display = 'none';
            $('previewContainer').innerHTML = '';
        }

        async function handlePreviewAttachment(attachmentId) {
            if (!attachmentId) {
                showToast('无法预览附件');
                return;
            }

            try {
                const response = await fetch(`${API_URL}/attachments/${attachmentId}/info`, {
                    headers: {
                        'user-id': JSON.parse(localStorage.getItem('user')).id
                    }
                });
                
                if (!response.ok) {
                    throw new Error('获取附件信息失败');
                }
                
                const attachment = await response.json();
                
                if (!attachment || !attachment.file_type) {
                    throw new Error('附件信息不完整');
                }
                
                showPreview(attachment);
            } catch (error) {
                showToast(error.message || '预览失败');
            }
        }

        async function handleDeleteAttachment(event, attachmentId) {
            event.stopPropagation();
            
            if (!confirm('确定要删除这个附件吗？')) {
                return;
            }

            try {
                await api.deleteAttachment(attachmentId);
                showToast('附件删除成功');
                await loadDocs();
            } catch (error) {
                showToast(error.message || '删除失败');
            }
        }

        // 用户管理相关函数
        let currentDeactivateUserId = null;
        let allUsers = [];

        async function showUserManage() {
            try {
                const users = await api.getUsers();
                allUsers = users;
                renderUserList(users);
                updateUserStats(users);
                $('userManageModal').style.display = 'flex';
                $('userSearchInput').value = '';
            } catch (error) {
                showToast(error.message || '加载用户列表失败');
            }
        }

        function hideUserManage() {
            $('userManageModal').style.display = 'none';
        }

        function updateUserStats(users) {
            const totalUsers = users.length;
            const adminUsers = users.filter(user => user.roles && user.roles.includes('admin')).length;
            const normalUsers = totalUsers - adminUsers;

            $('totalUsers').textContent = totalUsers;
            $('adminUsers').textContent = adminUsers;
            $('normalUsers').textContent = normalUsers;
        }

        function filterUsers() {
            const searchTerm = $('userSearchInput').value.toLowerCase();
            const filteredUsers = allUsers.filter(user =>
                user.username.toLowerCase().includes(searchTerm)
            );
            renderUserList(filteredUsers);
        }

        async function refreshUserList() {
            try {
                const users = await api.getUsers();
                allUsers = users;
                renderUserList(users);
                updateUserStats(users);
                $('userSearchInput').value = '';
                showToast('用户列表已刷新');
            } catch (error) {
                showToast(error.message || '刷新失败');
            }
        }

        function renderUserList(users) {
            const tbody = $('userList');
            const currentUser = JSON.parse(localStorage.getItem('user'));

            if (users.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" style="text-align: center; padding: 2rem; color: #999;">
                            📭 没有找到匹配的用户
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = users.map(user => {
                const isCurrentUser = user.id === currentUser.id;
                const isAdmin = user.roles && user.roles.includes('admin');
                const userInitial = user.username.charAt(0).toUpperCase();

                return `
                    <tr>
                        <td>
                            <div class="user-info">
                                <div class="user-avatar">${userInitial}</div>
                                <div class="user-details">
                                    <div class="user-name">${user.username}</div>
                                    <div class="user-badge">${isCurrentUser ? '👤 当前用户' : '👥 系统用户'}</div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="role-badge ${isAdmin ? 'role-admin' : 'role-user'}">
                                ${isAdmin ? '👑 管理员' : '👤 普通用户'}
                            </span>
                        </td>
                        <td>
                            <div style="font-size: 0.9rem;">
                                📅 ${new Date(user.created_at).toLocaleDateString('zh-CN')}
                                <br>
                                <span style="color: #999; font-size: 0.8rem;">
                                    🕐 ${new Date(user.created_at).toLocaleTimeString('zh-CN')}
                                </span>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge">✅ 正常</span>
                        </td>
                        <td>
                            ${!isCurrentUser && !isAdmin ?
                                `<button onclick="showDeactivateUser(${user.id}, '${user.username}')" class="action-btn deactivate-btn">
                                    🗑️ 注销账号
                                </button>` :
                                '<span class="disabled-action">🔒 不可操作</span>'
                            }
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function showDeactivateUser(userId, username) {
            currentDeactivateUserId = userId;
            $('deactivateUsername').textContent = username;
            $('deactivateUserError').textContent = '';
            $('deactivateUserError').style.display = 'none';
            $('deactivateUserModal').style.display = 'flex';
        }

        function hideDeactivateUser() {
            currentDeactivateUserId = null;
            $('deactivateUserModal').style.display = 'none';
            $('deactivateUserError').textContent = '';
            $('deactivateUserError').style.display = 'none';
        }

        async function doDeactivateUser() {
            const submitBtn = document.querySelector('#deactivateUserModal .danger-btn');
            const originalText = submitBtn.textContent;

            submitBtn.disabled = true;
            submitBtn.textContent = '⏳ 注销中...';

            try {
                const response = await api.deactivateUser({
                    targetUserId: currentDeactivateUserId
                });

                hideDeactivateUser();
                showToast('✅ 用户账号已注销！该用户需要重新注册才能使用系统。');

                // 刷新用户列表
                await refreshUserList();

            } catch (error) {
                $('deactivateUserError').textContent = '❌ ' + error.message;
                $('deactivateUserError').style.display = 'block';
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = originalText;
            }
        }

     // 模板相关函数
     async function showTemplates() {
         try {
             const templates = await api.getTemplates();
             renderTemplatesList(templates);
             $('templatesModal').style.display = 'flex';
         } catch (error) {
             showToast(error.message || '加载模板列表失败');
         }
     }

     function hideTemplates() {
         $('templatesModal').style.display = 'none';
     }

     function renderTemplatesList(templates) {
         const container = $('templatesList');
         
         if (templates.length === 0) {
             container.innerHTML = '<p style="text-align: center; color: #999; padding: 20px;">暂无模板文件</p>';
             return;
         }

         container.innerHTML = templates.map(template => `
             <div class="template-item">
                 <div class="template-info">
                     <div class="template-name">${template.name}</div>
                     <div class="template-details">
                         ${template.file_type_name} • ${formatFileSize(template.size)} • 
                         ${template.description ? template.description + ' • ' : ''}
                         上传者：${template.uploader_name} • 
                         ${new Date(template.created_at).toLocaleDateString()}
                     </div>
                 </div>
                 <div class="template-actions">
                     <button onclick="downloadTemplate(${template.id})" class="download-btn">
                         📥 下载
                     </button>
                 </div>
             </div>
         `).join('');
     }

     // 添加下载状态管理，防止重复下载
     let downloadingTemplates = new Set();

     async function downloadTemplate(templateId) {
         // 防止重复下载
         if (downloadingTemplates.has(templateId)) {
             showToast('正在下载中，请稍候...');
             return;
         }

         try {
             downloadingTemplates.add(templateId);
             
             const response = await api.downloadTemplate(templateId);
             
             if (!response.ok) {
                 throw new Error('下载失败');
             }
             
             // 获取文件名
             const contentDisposition = response.headers.get('Content-Disposition');
             let filename = 'template';
             if (contentDisposition) {
                 // 首先尝试RFC 5987格式 filename*=UTF-8''filename
                 const utf8Matches = contentDisposition.match(/filename\*=UTF-8''(.+)/);
                 if (utf8Matches) {
                     filename = decodeURIComponent(utf8Matches[1]);
                 } else {
                     // 备用：尝试传统格式 filename="filename"
                     const matches = contentDisposition.match(/filename="(.+)"/);
                     if (matches) {
                         filename = matches[1];
                     }
                 }
             }
             
             const blob = await response.blob();
             const url = URL.createObjectURL(blob);
             const a = document.createElement('a');
             a.href = url;
             a.download = filename;
             a.click();
             URL.revokeObjectURL(url);
             
             showToast('模板下载成功！');
         } catch (error) {
             showToast(error.message || '下载失败');
         } finally {
             downloadingTemplates.delete(templateId);
         }
     }

     // 模板管理函数（仅管理员）
     async function showTemplateManage() {
         try {
             const templates = await api.getTemplates();
             renderTemplatesManageList(templates);
             $('templateManageModal').style.display = 'flex';
         } catch (error) {
             showToast(error.message || '加载模板列表失败');
         }
     }

     function hideTemplateManage() {
         $('templateManageModal').style.display = 'none';
         // 清空表单
         $('templateName').value = '';
         $('templateDescription').value = '';
         $('templateFile').value = '';
         $('templateUploadError').textContent = '';
     }

     function renderTemplatesManageList(templates) {
         const tbody = $('templatesManageList');
         
         if (templates.length === 0) {
             tbody.innerHTML = '<tr><td colspan="4" style="text-align: center; color: #999; padding: 20px;">暂无模板文件</td></tr>';
             return;
         }

         tbody.innerHTML = templates.map(template => `
             <tr>
                 <td title="${template.filename}">${template.filename}</td>
                 <td>${template.file_type_name}</td>
                 <td>${new Date(template.created_at).toLocaleString()}</td>
                 <td>
                     <button onclick="downloadTemplate(${template.id})" class="download-btn">下载</button>
                     <button onclick="deleteTemplate(${template.id}, '${template.name}')" class="delete-template-btn">删除</button>
                 </td>
             </tr>
         `).join('');
     }

     async function uploadTemplate() {
         const name = $('templateName').value.trim();
         const description = $('templateDescription').value.trim();
         const fileInput = $('templateFile');
         const file = fileInput.files[0];

         // 清除错误提示
         $('templateUploadError').textContent = '';

         if (!name) {
             $('templateUploadError').textContent = '请输入模板名称';
             return;
         }

         if (!file) {
             $('templateUploadError').textContent = '请选择要上传的模板文件';
             return;
         }

         // 检查文件大小
         if (file.size > 50 * 1024 * 1024) {
             $('templateUploadError').textContent = '文件大小不能超过50MB';
             return;
         }

         try {
             const formData = new FormData();
             formData.append('template', file);
             formData.append('name', name);
             formData.append('description', description);

             await api.uploadTemplate(formData);
             
             showToast('模板上传成功！');
             
             // 清空表单
             $('templateName').value = '';
             $('templateDescription').value = '';
             $('templateFile').value = '';
             
             // 刷新列表
             const templates = await api.getTemplates();
             renderTemplatesManageList(templates);
         } catch (error) {
             $('templateUploadError').textContent = error.message || '上传失败，请重试';
         }
     }

     async function deleteTemplate(templateId, templateName) {
         if (!confirm(`确定要删除模板"${templateName}"吗？此操作不可撤销！`)) {
             return;
         }

         try {
             await api.deleteTemplate(templateId);
             showToast('模板删除成功！');
             
             // 刷新列表
             const templates = await api.getTemplates();
             renderTemplatesManageList(templates);
         } catch (error) {
             showToast(error.message || '删除失败');
         }
     }

        // 查询功能相关函数
        async function showSearchPanel() {
            const panel = $('searchPanel');
            if (panel.style.display === 'none') {
                panel.style.display = 'block';
                await initSearchOptions();
                $('searchPanel').scrollIntoView({ behavior: 'smooth' });
            }
        }

        function toggleSearchPanel() {
            const panel = $('searchPanel');
            if (panel.style.display === 'block') {
                panel.style.display = 'none';
            } else {
                showSearchPanel();
            }
        }

        async function initSearchOptions() {
            try {
                const options = await api.getSearchOptions();

                // 填充年份选项
                const yearSelect = $('searchYear');
                yearSelect.innerHTML = '<option value="">全部年份</option>';
                options.years.forEach(year => {
                    yearSelect.innerHTML += `<option value="${year}">${year}年</option>`;
                });

                // 填充创建者选项
                const creatorSelect = $('searchCreator');
                creatorSelect.innerHTML = '<option value="">全部用户</option>';
                options.creators.forEach(creator => {
                    creatorSelect.innerHTML += `<option value="${creator.id}">${creator.username}</option>`;
                });

            } catch (error) {
                console.error('加载查询选项失败:', error);
            }
        }

        async function performSearch() {
            const searchParams = {
                type: $('searchType').value,
                year: $('searchYear').value,
                creator: $('searchCreator').value,
                startDate: $('searchStartDate').value,
                endDate: $('searchEndDate').value,
                number: $('searchNumber').value,
                keyword: $('searchKeyword').value.trim()
            };

            // 移除空值
            Object.keys(searchParams).forEach(key => {
                if (!searchParams[key]) {
                    delete searchParams[key];
                }
            });

            try {
                currentSearchParams = searchParams;
                isSearchMode = Object.keys(searchParams).length > 0;
                currentPage = 1;

                if (isSearchMode) {
                    const result = await api.searchDocs({ ...searchParams, page: currentPage });
                    renderDocumentList(result.documents);
                    updatePagination(result.pagination);
                    showSearchResultInfo(result.total, searchParams);
                } else {
                    await loadDocs();
                    hideSearchResultInfo();
                }

            } catch (error) {
                showToast('查询失败: ' + error.message);
            }
        }

        function clearSearch() {
            $('searchType').value = '';
            $('searchYear').value = '';
            $('searchCreator').value = '';
            $('searchStartDate').value = '';
            $('searchEndDate').value = '';
            $('searchNumber').value = '';
            $('searchKeyword').value = '';
        }

        async function resetToAll() {
            clearSearch();
            currentSearchParams = null;
            isSearchMode = false;
            currentPage = 1;
            await loadDocs();
            hideSearchResultInfo();
        }

        function showSearchResultInfo(total, params) {
            let info = `🔍 查询结果：共找到 ${total} 个文档`;

            const conditions = [];
            if (params.type) conditions.push(`类型：${params.type}`);
            if (params.year) conditions.push(`年份：${params.year}`);
            if (params.creator) {
                const creatorSelect = $('searchCreator');
                const creatorName = creatorSelect.options[creatorSelect.selectedIndex].text;
                conditions.push(`创建者：${creatorName}`);
            }
            if (params.startDate || params.endDate) {
                const dateRange = `${params.startDate || '开始'} 至 ${params.endDate || '结束'}`;
                conditions.push(`时间：${dateRange}`);
            }
            if (params.number) conditions.push(`文档号：${params.number}`);
            if (params.keyword) conditions.push(`关键词：${params.keyword}`);

            if (conditions.length > 0) {
                info += ` (${conditions.join('，')})`;
            }

            info += '<span class="clear-search" onclick="resetToAll()">清除查询</span>';

            // 在文档列表前显示查询结果信息
            let resultInfo = document.querySelector('.search-result-info');
            if (!resultInfo) {
                resultInfo = document.createElement('div');
                resultInfo.className = 'search-result-info';
                const docList = document.querySelector('.table-container');
                docList.parentNode.insertBefore(resultInfo, docList);
            }
            resultInfo.innerHTML = info;
            resultInfo.style.display = 'block';
        }

        function hideSearchResultInfo() {
            const resultInfo = document.querySelector('.search-result-info');
            if (resultInfo) {
                resultInfo.style.display = 'none';
            }
        }

        // 渲染文档列表
        function renderDocumentList(documents) {
            $('docList').innerHTML = documents.map(doc => `
                <tr data-doc-id="${doc.id}">
                    <td>${new Date(doc.created_at).toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' })}</td>
                    <td>${doc.type}</td>
                    <td>${doc.formatted_number}</td>
                    <td class="doc-title-cell">
                        <div class="doc-title-wrapper">
                            <div class="doc-title" title="${doc.title}">
                                ${doc.title}
                            </div>
                            <div class="action-buttons">
                                <button class="icon-btn" onclick="showEditTitleModal(${doc.id}, '${doc.title}')" title="修改标题">
                                    <span class="icon">✎</span>
                                </button>
                            </div>
                        </div>
                    </td>
                    <td>${doc.creator_name || '未知'}</td>
                    <td class="upload-area">
                            <div class="attachments-preview">
                            ${doc.attachments && doc.attachments.length > 0 ?
                                doc.attachments.map(attachment => `
                                    <div class="attachment-item" onclick="handlePreviewAttachment(${attachment.id})">
                                        <div class="attachment-preview">
                                            ${attachment.file_type === 'image'
                                                ? `<img src="${attachment.filepath}" alt="附件预览">`
                                                : getFileIcon(attachment.file_type)
                                            }
                            </div>
                                        <div class="attachment-info">
                                            <div class="attachment-name" title="${attachment.filename}">
                                                ${attachment.filename}
                                            </div>
                                            <div class="attachment-type">
                                                ${getFileTypeName(attachment.file_type)}
                                                <span class="attachment-size">${formatFileSize(attachment.size || 0)}</span>
                                            </div>
                                        </div>
                                        <div class="attachment-delete" onclick="handleDeleteAttachment(event, ${attachment.id})">×</div>
                                    </div>
                                `).join('')
                                : ''
                            }
                            <label class="upload-label" for="file${doc.id}">
                                <span class="icon">➕</span> 上传附件
                            </label>
                        </div>
                        <input type="file" id="file${doc.id}" multiple
                            onchange="handleUpload(${doc.id}, this.files)"
                            style="display:none">
                    </td>
                    <td>
                        <button class="secondary-btn" onclick="handleDelete(${doc.id})">删除</button>
                    </td>
                </tr>
            `).join('');
        }

        // 更新分页信息
        function updatePagination(pagination) {
            totalPages = pagination.totalPages;
            currentPage = pagination.currentPage;

            $('pageInfo').textContent = `第 ${currentPage} 页 / 共 ${totalPages} 页`;
            $('prevPage').disabled = currentPage < 2;
            $('nextPage').disabled = currentPage >= totalPages;
        }


</script>
</body>
</html>