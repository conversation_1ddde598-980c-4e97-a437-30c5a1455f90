const sqlite3 = require('sqlite3').verbose();
const fs = require('fs');
const path = require('path');

// 数据库文件路径
const dbFile = 'documents.db';

// 数据库工具函数
const dbAsync = (db, method, sql, params = []) => {
    return new Promise((resolve, reject) => {
        db[method](sql, params, (err, result) => {
            err ? reject(err) : resolve(result);
        });
    });
};

async function clearDocuments() {
    const db = new sqlite3.Database(dbFile);
    
    try {
        console.log('开始清空发文数据...');
        
        // 开始事务
        await dbAsync(db, 'run', 'BEGIN TRANSACTION');
        
        // 1. 获取所有附件文件路径
        console.log('获取附件文件列表...');
        const attachments = await dbAsync(db, 'all', 'SELECT filepath FROM attachments');
        
        // 2. 删除附件文件
        console.log(`删除 ${attachments.length} 个附件文件...`);
        for (const attachment of attachments) {
            if (fs.existsSync(attachment.filepath)) {
                try {
                    fs.unlinkSync(attachment.filepath);
                    console.log(`已删除文件: ${attachment.filepath}`);
                } catch (err) {
                    console.warn(`删除文件失败: ${attachment.filepath}, 错误: ${err.message}`);
                }
            }
        }
        
        // 3. 清空数据库表
        console.log('清空数据库表...');
        
        // 删除附件记录
        await dbAsync(db, 'run', 'DELETE FROM attachments');
        console.log('已清空 attachments 表');
        
        // 删除文档历史记录（如果存在）
        try {
            await dbAsync(db, 'run', 'DELETE FROM document_history');
            console.log('已清空 document_history 表');
        } catch (err) {
            // 表可能不存在，忽略错误
            console.log('document_history 表不存在，跳过');
        }
        
        // 删除文档记录
        await dbAsync(db, 'run', 'DELETE FROM documents');
        console.log('已清空 documents 表');
        
        // 重置自增ID
        await dbAsync(db, 'run', 'DELETE FROM sqlite_sequence WHERE name IN ("documents", "attachments", "document_history")');
        console.log('已重置自增ID');
        
        // 提交事务
        await dbAsync(db, 'run', 'COMMIT');
        
        // 4. 清空uploads目录中的文件（保留目录结构）
        console.log('清空uploads目录...');
        const uploadsDir = path.join(__dirname, 'uploads');
        if (fs.existsSync(uploadsDir)) {
            const files = fs.readdirSync(uploadsDir);
            for (const file of files) {
                const filePath = path.join(uploadsDir, file);
                const stat = fs.statSync(filePath);
                
                if (stat.isFile()) {
                    try {
                        fs.unlinkSync(filePath);
                        console.log(`已删除uploads文件: ${file}`);
                    } catch (err) {
                        console.warn(`删除uploads文件失败: ${file}, 错误: ${err.message}`);
                    }
                }
            }
        }
        
        console.log('✅ 发文数据清空完成！');
        console.log('📋 清空内容包括：');
        console.log('   - 所有文档记录');
        console.log('   - 所有附件记录');
        console.log('   - 所有附件文件');
        console.log('   - uploads目录中的文件');
        console.log('   - 重置了文档编号（下次取号将从1开始）');
        console.log('');
        console.log('⚠️  注意：用户账号、角色权限、模板文件均已保留');
        
    } catch (error) {
        // 回滚事务
        try {
            await dbAsync(db, 'run', 'ROLLBACK');
        } catch (rollbackErr) {
            console.error('回滚失败:', rollbackErr);
        }
        
        console.error('❌ 清空发文数据失败:', error);
        throw error;
    } finally {
        // 关闭数据库连接
        db.close((err) => {
            if (err) {
                console.error('关闭数据库连接失败:', err);
            } else {
                console.log('数据库连接已关闭');
            }
        });
    }
}

// 确认提示
function confirmClear() {
    return new Promise((resolve) => {
        const readline = require('readline');
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        console.log('⚠️  警告：此操作将清空所有发文数据，包括：');
        console.log('   - 所有文档记录');
        console.log('   - 所有附件文件');
        console.log('   - uploads目录中的文件');
        console.log('');
        console.log('✅ 以下数据将保留：');
        console.log('   - 用户账号和密码');
        console.log('   - 角色和权限设置');
        console.log('   - 模板文件');
        console.log('');
        
        rl.question('确定要继续吗？(输入 "yes" 确认): ', (answer) => {
            rl.close();
            resolve(answer.toLowerCase() === 'yes');
        });
    });
}

// 主函数
async function main() {
    try {
        const confirmed = await confirmClear();
        
        if (!confirmed) {
            console.log('操作已取消');
            return;
        }
        
        await clearDocuments();
        
    } catch (error) {
        console.error('执行失败:', error);
        process.exit(1);
    }
}

// 运行脚本
if (require.main === module) {
    main();
}

module.exports = { clearDocuments };
